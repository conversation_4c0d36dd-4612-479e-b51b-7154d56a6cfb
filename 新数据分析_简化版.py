#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
新数据资金流向分析 - 简化版
数据文件：merged_data_20250122_20250724.csv
实现三种筛选策略的6个具体条件
"""

import pandas as pd
from datetime import datetime

def main():
    print("=" * 60)
    print("新数据资金流向与收益率关系分析")
    print("=" * 60)
    
    # 加载数据
    csv_file = "merged_data_20250122_20250724.csv"
    print(f"正在加载数据文件: {csv_file}")
    
    try:
        df = pd.read_csv(csv_file, encoding='utf-8')
        print(f"原始数据加载成功：{len(df)} 条记录")
        
        # 显示列名
        print(f"\n数据列名（共{len(df.columns)}列）:")
        for i, col in enumerate(df.columns):
            print(f"{i+1:2d}. {col}")
        
        # 资金流向列
        fund_flow_columns = [
            'FundFlow_主力净流入-净占比',
            'FundFlow_超大单净流入-净占比',
            'FundFlow_大单净流入-净占比',
            'FundFlow_中单净流入-净占比',
            'FundFlow_小单净流入-净占比'
        ]
        
        # 检查必要列
        missing_cols = [col for col in fund_flow_columns + ['收益率'] if col not in df.columns]
        if missing_cols:
            print(f"缺少必要的列: {missing_cols}")
            return
        
        # 数据清洗
        print("\n开始数据清洗...")
        original_count = len(df)
        
        # 转换数据类型
        for col in fund_flow_columns:
            df[col] = pd.to_numeric(df[col], errors='coerce')
        df['收益率'] = pd.to_numeric(df['收益率'], errors='coerce')
        
        # 删除空值
        df = df.dropna(subset=fund_flow_columns + ['收益率'])
        cleaned_count = len(df)
        print(f"数据清洗完成：{cleaned_count} 条有效记录（删除了 {original_count - cleaned_count} 条无效记录）")
        
        # 基准统计
        print("\n" + "=" * 50)
        print("基准统计（无筛选条件）")
        print("=" * 50)
        
        total_records = len(df)
        
        # 收益率>0%
        positive_mask = df['收益率'] > 0
        positive_count = positive_mask.sum()
        positive_mean = df.loc[positive_mask, '收益率'].mean() if positive_count > 0 else 0
        positive_pct = (positive_count / total_records * 100) if total_records > 0 else 0
        
        # 收益率>1%
        gt1_mask = df['收益率'] > 1
        gt1_count = gt1_mask.sum()
        gt1_mean = df.loc[gt1_mask, '收益率'].mean() if gt1_count > 0 else 0
        gt1_pct = (gt1_count / total_records * 100) if total_records > 0 else 0
        
        # 收益率>3%
        gt3_mask = df['收益率'] > 3
        gt3_count = gt3_mask.sum()
        gt3_mean = df.loc[gt3_mask, '收益率'].mean() if gt3_count > 0 else 0
        gt3_pct = (gt3_count / total_records * 100) if total_records > 0 else 0
        
        print(f"总记录数: {total_records}")
        print(f"收益率 > 0%: {positive_count} 条 ({positive_pct:.2f}%), 平均收益率: {positive_mean:.2f}%")
        print(f"收益率 > 1%: {gt1_count} 条 ({gt1_pct:.2f}%), 平均收益率: {gt1_mean:.2f}%")
        print(f"收益率 > 3%: {gt3_count} 条 ({gt3_pct:.2f}%), 平均收益率: {gt3_mean:.2f}%")
        
        # 定义筛选条件
        conditions = [
            # 强筛选策略
            {
                'name': '强筛选策略1',
                'description': '主力净流入净占比 > 10%',
                'strategy': '强筛选策略',
                'filter': df['FundFlow_主力净流入-净占比'] > 10
            },
            {
                'name': '强筛选策略2',
                'description': '所有5个资金流向指标均为正值',
                'strategy': '强筛选策略',
                'filter': (df[fund_flow_columns] > 0).all(axis=1)
            },
            
            # 平衡策略
            {
                'name': '平衡策略1',
                'description': '主力净流入净占比 > 5%',
                'strategy': '平衡策略',
                'filter': df['FundFlow_主力净流入-净占比'] > 5
            },
            {
                'name': '平衡策略2',
                'description': '至少4个资金流向指标为正值',
                'strategy': '平衡策略',
                'filter': (df[fund_flow_columns] > 0).sum(axis=1) >= 4
            },
            
            # 宽松策略
            {
                'name': '宽松策略1',
                'description': '主力净流入净占比 > 0%（为正值）',
                'strategy': '宽松策略',
                'filter': df['FundFlow_主力净流入-净占比'] > 0
            },
            {
                'name': '宽松策略2',
                'description': '至少3个资金流向指标为正值',
                'strategy': '宽松策略',
                'filter': (df[fund_flow_columns] > 0).sum(axis=1) >= 3
            }
        ]
        
        # 分析结果
        results = []
        
        print("\n" + "=" * 80)
        print("筛选条件分析结果")
        print("=" * 80)
        
        for condition in conditions:
            filtered_df = df[condition['filter']]
            filtered_count = len(filtered_df)
            
            if filtered_count == 0:
                print(f"\n【{condition['strategy']}】{condition['description']}: 无符合条件的记录")
                continue
            
            # 计算统计
            f_positive_mask = filtered_df['收益率'] > 0
            f_positive_count = f_positive_mask.sum()
            f_positive_mean = filtered_df.loc[f_positive_mask, '收益率'].mean() if f_positive_count > 0 else 0
            f_positive_pct = (f_positive_count / filtered_count * 100) if filtered_count > 0 else 0
            f_positive_improvement = f_positive_pct - positive_pct
            
            f_gt1_mask = filtered_df['收益率'] > 1
            f_gt1_count = f_gt1_mask.sum()
            f_gt1_mean = filtered_df.loc[f_gt1_mask, '收益率'].mean() if f_gt1_count > 0 else 0
            f_gt1_pct = (f_gt1_count / filtered_count * 100) if filtered_count > 0 else 0
            f_gt1_improvement = f_gt1_pct - gt1_pct
            
            f_gt3_mask = filtered_df['收益率'] > 3
            f_gt3_count = f_gt3_mask.sum()
            f_gt3_mean = filtered_df.loc[f_gt3_mask, '收益率'].mean() if f_gt3_count > 0 else 0
            f_gt3_pct = (f_gt3_count / filtered_count * 100) if filtered_count > 0 else 0
            f_gt3_improvement = f_gt3_pct - gt3_pct
            
            print(f"\n【{condition['strategy']}】{condition['description']}")
            print(f"  筛选后记录数: {filtered_count} 条")
            print(f"  收益率>0%: {f_positive_count}条 ({f_positive_pct:.1f}%) 改善: {f_positive_improvement:+.1f}% 平均: {f_positive_mean:.2f}%")
            print(f"  收益率>1%: {f_gt1_count}条 ({f_gt1_pct:.1f}%) 改善: {f_gt1_improvement:+.1f}% 平均: {f_gt1_mean:.2f}%")
            print(f"  收益率>3%: {f_gt3_count}条 ({f_gt3_pct:.1f}%) 改善: {f_gt3_improvement:+.1f}% 平均: {f_gt3_mean:.2f}%")
            
            # 保存结果
            results.append({
                '筛选策略名称': condition['strategy'],
                '筛选条件描述': condition['description'],
                '筛选后记录数量': filtered_count,
                '收益率>0%_数量': f_positive_count,
                '收益率>0%_平均收益率': round(f_positive_mean, 2),
                '收益率>0%_占比百分比': round(f_positive_pct, 2),
                '收益率>1%_数量': f_gt1_count,
                '收益率>1%_平均收益率': round(f_gt1_mean, 2),
                '收益率>1%_占比百分比': round(f_gt1_pct, 2),
                '收益率>3%_数量': f_gt3_count,
                '收益率>3%_平均收益率': round(f_gt3_mean, 2),
                '收益率>3%_占比百分比': round(f_gt3_pct, 2),
                '相对基准改善率_正收益': round(f_positive_improvement, 2),
                '相对基准改善率_1%以上': round(f_gt1_improvement, 2),
                '相对基准改善率_3%以上': round(f_gt3_improvement, 2)
            })
        
        # 保存结果到CSV
        if results:
            # 添加基准数据
            baseline_result = {
                '筛选策略名称': '基准',
                '筛选条件描述': '基准（无筛选）',
                '筛选后记录数量': total_records,
                '收益率>0%_数量': positive_count,
                '收益率>0%_平均收益率': round(positive_mean, 2),
                '收益率>0%_占比百分比': round(positive_pct, 2),
                '收益率>1%_数量': gt1_count,
                '收益率>1%_平均收益率': round(gt1_mean, 2),
                '收益率>1%_占比百分比': round(gt1_pct, 2),
                '收益率>3%_数量': gt3_count,
                '收益率>3%_平均收益率': round(gt3_mean, 2),
                '收益率>3%_占比百分比': round(gt3_pct, 2),
                '相对基准改善率_正收益': 0,
                '相对基准改善率_1%以上': 0,
                '相对基准改善率_3%以上': 0
            }
            
            all_results = [baseline_result] + results
            results_df = pd.DataFrame(all_results)
            
            # 按>3%收益改善率排序
            results_df = results_df.sort_values('相对基准改善率_3%以上', ascending=False)
            
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            output_file = f'新数据资金流向分析结果_{timestamp}.csv'
            results_df.to_csv(output_file, index=False, encoding='utf-8-sig')
            
            print(f"\n" + "=" * 80)
            print("分析完成！")
            print("=" * 80)
            print(f"分析结果已保存到: {output_file}")
            
            # 显示最佳条件
            print(f"\n最佳筛选条件（按>3%收益改善率排序）:")
            print("-" * 60)
            for _, row in results_df.head(4).iterrows():
                if row['筛选条件描述'] != '基准（无筛选）':
                    print(f"【{row['筛选策略名称']}】{row['筛选条件描述']}")
                    print(f"  >3%收益改善: {row['相对基准改善率_3%以上']:+.1f}%, 样本数量: {row['筛选后记录数量']}条")
        
        print("\n分析完成！")
        
    except Exception as e:
        print(f"程序执行出错: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
