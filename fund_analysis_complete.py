#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
资金流向与收益率关系完整分析程序
"""

import pandas as pd
import numpy as np
from datetime import datetime
import os

def main():
    print("=" * 60)
    print("资金流向与收益率关系深度分析")
    print("=" * 60)
    
    # 文件路径
    csv_file = "merged_data_20250601_20250630.csv"
    
    try:
        # 1. 加载数据
        print("1. 加载数据...")
        df = pd.read_csv(csv_file, encoding='utf-8')
        print(f"   原始数据: {len(df)} 条记录")
        
        # 2. 数据清洗
        print("2. 数据清洗...")
        
        # 资金流向列
        fund_flow_columns = [
            'FundFlow_主力净流入-净占比',
            'FundFlow_超大单净流入-净占比',
            'FundFlow_大单净流入-净占比',
            'FundFlow_中单净流入-净占比',
            'FundFlow_小单净流入-净占比'
        ]
        
        # 检查列是否存在
        missing_cols = [col for col in fund_flow_columns + ['收益率'] if col not in df.columns]
        if missing_cols:
            print(f"   错误: 缺少列 {missing_cols}")
            print(f"   可用列: {list(df.columns)}")
            return
        
        # 转换数据类型
        for col in fund_flow_columns:
            df[col] = pd.to_numeric(df[col], errors='coerce')
        df['收益率'] = pd.to_numeric(df['收益率'], errors='coerce')
        
        # 删除空值
        original_count = len(df)
        df = df.dropna(subset=fund_flow_columns + ['收益率'])
        cleaned_count = len(df)
        print(f"   清洗后数据: {cleaned_count} 条记录 (删除了 {original_count - cleaned_count} 条)")
        
        # 3. 基准统计
        print("\n3. 基准统计（无筛选条件）")
        print("-" * 40)
        
        total_records = len(df)
        
        # 收益率>0%
        positive_mask = df['收益率'] > 0
        positive_count = positive_mask.sum()
        positive_mean = df.loc[positive_mask, '收益率'].mean() if positive_count > 0 else 0
        positive_pct = (positive_count / total_records * 100) if total_records > 0 else 0
        
        # 收益率>1%
        gt1_mask = df['收益率'] > 1
        gt1_count = gt1_mask.sum()
        gt1_mean = df.loc[gt1_mask, '收益率'].mean() if gt1_count > 0 else 0
        gt1_pct = (gt1_count / total_records * 100) if total_records > 0 else 0
        
        # 收益率>3%
        gt3_mask = df['收益率'] > 3
        gt3_count = gt3_mask.sum()
        gt3_mean = df.loc[gt3_mask, '收益率'].mean() if gt3_count > 0 else 0
        gt3_pct = (gt3_count / total_records * 100) if total_records > 0 else 0
        
        print(f"总记录数: {total_records}")
        print(f"收益率 > 0%: {positive_count} 条 ({positive_pct:.2f}%), 平均: {positive_mean:.2f}%")
        print(f"收益率 > 1%: {gt1_count} 条 ({gt1_pct:.2f}%), 平均: {gt1_mean:.2f}%")
        print(f"收益率 > 3%: {gt3_count} 条 ({gt3_pct:.2f}%), 平均: {gt3_mean:.2f}%")
        
        # 4. 筛选条件分析
        print("\n4. 筛选条件分析")
        print("-" * 40)
        
        # 定义筛选条件
        conditions = [
            {
                'name': '所有指标均为正值',
                'filter': (df[fund_flow_columns] > 0).all(axis=1),
                'description': '所有5个资金流向指标均为正值'
            },
            {
                'name': '主力净流入为正值',
                'filter': df['FundFlow_主力净流入-净占比'] > 0,
                'description': '主力净流入净占比 > 0%'
            },
            {
                'name': '主力净流入>5%',
                'filter': df['FundFlow_主力净流入-净占比'] > 5,
                'description': '主力净流入净占比 > 5%'
            },
            {
                'name': '主力净流入>10%',
                'filter': df['FundFlow_主力净流入-净占比'] > 10,
                'description': '主力净流入净占比 > 10%'
            },
            {
                'name': '主力+超大单均为正',
                'filter': (df['FundFlow_主力净流入-净占比'] > 0) & (df['FundFlow_超大单净流入-净占比'] > 0),
                'description': '主力净流入和超大单净流入均为正值'
            },
            {
                'name': '至少3个指标为正',
                'filter': (df[fund_flow_columns] > 0).sum(axis=1) >= 3,
                'description': '至少3个资金流向指标为正值'
            },
            {
                'name': '至少4个指标为正',
                'filter': (df[fund_flow_columns] > 0).sum(axis=1) >= 4,
                'description': '至少4个资金流向指标为正值'
            },
            {
                'name': '指标总和>10%',
                'filter': df[fund_flow_columns].sum(axis=1) > 10,
                'description': '所有资金流向指标总和 > 10%'
            },
            {
                'name': '指标总和>20%',
                'filter': df[fund_flow_columns].sum(axis=1) > 20,
                'description': '所有资金流向指标总和 > 20%'
            },
            {
                'name': '大中小单均为正',
                'filter': (df['FundFlow_大单净流入-净占比'] > 0) & 
                         (df['FundFlow_中单净流入-净占比'] > 0) & 
                         (df['FundFlow_小单净流入-净占比'] > 0),
                'description': '大单、中单、小单净流入均为正值'
            }
        ]
        
        # 分析结果存储
        results = []
        
        for condition in conditions:
            filtered_df = df[condition['filter']]
            filtered_count = len(filtered_df)
            
            if filtered_count == 0:
                print(f"\n{condition['name']}: 无符合条件的记录")
                continue
            
            # 计算统计
            f_positive_mask = filtered_df['收益率'] > 0
            f_positive_count = f_positive_mask.sum()
            f_positive_pct = (f_positive_count / filtered_count * 100) if filtered_count > 0 else 0
            f_positive_improvement = f_positive_pct - positive_pct
            
            f_gt1_mask = filtered_df['收益率'] > 1
            f_gt1_count = f_gt1_mask.sum()
            f_gt1_pct = (f_gt1_count / filtered_count * 100) if filtered_count > 0 else 0
            f_gt1_improvement = f_gt1_pct - gt1_pct
            
            f_gt3_mask = filtered_df['收益率'] > 3
            f_gt3_count = f_gt3_mask.sum()
            f_gt3_pct = (f_gt3_count / filtered_count * 100) if filtered_count > 0 else 0
            f_gt3_improvement = f_gt3_pct - gt3_pct
            
            print(f"\n{condition['name']}:")
            print(f"  描述: {condition['description']}")
            print(f"  筛选后记录: {filtered_count} 条")
            print(f"  正收益率: {f_positive_pct:.2f}% (改善: {f_positive_improvement:+.2f}%)")
            print(f"  >1%收益率: {f_gt1_pct:.2f}% (改善: {f_gt1_improvement:+.2f}%)")
            print(f"  >3%收益率: {f_gt3_pct:.2f}% (改善: {f_gt3_improvement:+.2f}%)")
            
            # 保存结果
            results.append({
                '筛选条件': condition['name'],
                '条件描述': condition['description'],
                '记录数': filtered_count,
                '正收益率%': round(f_positive_pct, 2),
                '正收益改善%': round(f_positive_improvement, 2),
                '>1%收益率%': round(f_gt1_pct, 2),
                '>1%收益改善%': round(f_gt1_improvement, 2),
                '>3%收益率%': round(f_gt3_pct, 2),
                '>3%收益改善%': round(f_gt3_improvement, 2)
            })
        
        # 5. 保存结果
        if results:
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            output_file = f'资金流向分析结果_{timestamp}.csv'
            
            # 添加基准数据
            baseline_result = {
                '筛选条件': '基准（无筛选）',
                '条件描述': '所有数据，无筛选条件',
                '记录数': total_records,
                '正收益率%': round(positive_pct, 2),
                '正收益改善%': 0,
                '>1%收益率%': round(gt1_pct, 2),
                '>1%收益改善%': 0,
                '>3%收益率%': round(gt3_pct, 2),
                '>3%收益改善%': 0
            }
            
            all_results = [baseline_result] + results
            results_df = pd.DataFrame(all_results)
            
            # 按>3%收益改善率排序
            results_df = results_df.sort_values('>3%收益改善%', ascending=False)
            
            results_df.to_csv(output_file, index=False, encoding='utf-8-sig')
            print(f"\n5. 结果已保存到: {output_file}")
            
            # 显示最佳条件
            print("\n6. 最佳筛选条件（按>3%收益改善率排序）:")
            print("-" * 50)
            for i, row in results_df.head(5).iterrows():
                if row['筛选条件'] != '基准（无筛选）':
                    print(f"{row['筛选条件']}: >3%收益改善 {row['>3%收益改善%']:+.2f}%, 记录数 {row['记录数']}")
        
        print("\n分析完成！")
        
    except Exception as e:
        print(f"程序执行出错: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
