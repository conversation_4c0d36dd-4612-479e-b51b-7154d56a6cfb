#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
股票数据合并程序
功能：将主文件与资金流向数据按日期和股票代码进行合并
作者：AI Assistant
创建时间：2025-07-25
"""

import pandas as pd
import os
import glob
from datetime import datetime
import logging
from typing import Dict, List, Tuple, Optional

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('data_merger.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class DataMerger:
    """数据合并器类"""
    
    def __init__(self, main_file_path: str, data_source_dir: str):
        """
        初始化数据合并器
        
        Args:
            main_file_path: 主文件路径
            data_source_dir: 数据源目录路径
        """
        self.main_file_path = main_file_path
        self.data_source_dir = data_source_dir
        self.main_df = None
        self.merged_df = None
        self.stats = {
            'total_records': 0,
            'matched_records': 0,
            'unmatched_records': 0,
            'missing_files': 0,
            'date_mismatches': 0
        }
    
    def validate_inputs(self) -> bool:
        """验证输入文件和目录是否存在"""
        logger.info("开始验证输入文件和目录...")
        
        # 检查主文件
        if not os.path.exists(self.main_file_path):
            logger.error(f"主文件不存在: {self.main_file_path}")
            return False
        
        # 检查数据源目录
        if not os.path.exists(self.data_source_dir):
            logger.error(f"数据源目录不存在: {self.data_source_dir}")
            return False
        
        # 检查目录中是否有CSV文件
        csv_files = glob.glob(os.path.join(self.data_source_dir, "*.csv"))
        if not csv_files:
            logger.error(f"数据源目录中没有找到CSV文件: {self.data_source_dir}")
            return False
        
        logger.info(f"验证通过 - 主文件: {self.main_file_path}")
        logger.info(f"验证通过 - 数据源目录: {self.data_source_dir} (包含 {len(csv_files)} 个CSV文件)")
        return True
    
    def load_main_file(self) -> bool:
        """加载主文件并验证必要列"""
        try:
            logger.info("正在加载主文件...")
            self.main_df = pd.read_csv(self.main_file_path, encoding='utf-8')
            
            # 检查必要列是否存在
            required_columns = ['secID', '涨停日期']
            missing_columns = [col for col in required_columns if col not in self.main_df.columns]
            
            if missing_columns:
                logger.error(f"主文件缺少必要列: {missing_columns}")
                return False
            
            # 统计记录数
            self.stats['total_records'] = len(self.main_df)
            logger.info(f"主文件加载成功，共 {self.stats['total_records']} 条记录")
            logger.info(f"主文件列名: {list(self.main_df.columns)}")
            
            return True
            
        except Exception as e:
            logger.error(f"加载主文件失败: {str(e)}")
            return False
    
    def extract_stock_code(self, sec_id: str) -> str:
        """从SecID中提取6位股票代码"""
        if pd.isna(sec_id) or not isinstance(sec_id, str):
            return ""
        return sec_id[:6]
    
    def find_matching_file(self, stock_code: str) -> Optional[str]:
        """查找匹配的资金流向文件"""
        # 构建可能的文件名模式
        patterns = [
            f"{stock_code}_sz_fund_flow.csv",  # 深圳交易所
            f"{stock_code}_sh_fund_flow.csv"   # 上海交易所
        ]
        
        for pattern in patterns:
            file_path = os.path.join(self.data_source_dir, pattern)
            if os.path.exists(file_path):
                return file_path
        
        return None
    
    def standardize_date_format(self, date_str: str) -> str:
        """标准化日期格式"""
        if pd.isna(date_str):
            return ""
        
        try:
            # 尝试解析不同的日期格式
            date_str = str(date_str).strip()
            
            # 处理 "2025/6/18" 格式
            if '/' in date_str:
                parts = date_str.split('/')
                if len(parts) == 3:
                    year, month, day = parts
                    return f"{year}-{month.zfill(2)}-{day.zfill(2)}"
            
            # 处理 "2025-06-18" 格式
            if '-' in date_str and len(date_str.split('-')) == 3:
                return date_str
            
            # 其他格式尝试用pandas解析
            parsed_date = pd.to_datetime(date_str)
            return parsed_date.strftime('%Y-%m-%d')
            
        except Exception as e:
            logger.warning(f"日期格式转换失败: {date_str}, 错误: {str(e)}")
            return ""
    
    def merge_data(self) -> bool:
        """执行数据合并"""
        logger.info("开始数据合并过程...")
        
        # 复制主数据框
        self.merged_df = self.main_df.copy()
        
        # 为每行数据查找匹配的资金流向数据
        for index, row in self.main_df.iterrows():
            if index % 10 == 0:  # 每10条记录显示一次进度
                progress = (index + 1) / len(self.main_df) * 100
                logger.info(f"处理进度: {progress:.1f}% ({index + 1}/{len(self.main_df)})")
            
            # 提取股票代码
            stock_code = self.extract_stock_code(row['secID'])
            if not stock_code:
                logger.warning(f"第 {index + 1} 行: 无法提取股票代码 from {row['secID']}")
                self.stats['unmatched_records'] += 1
                continue
            
            # 查找匹配文件
            matching_file = self.find_matching_file(stock_code)
            if not matching_file:
                logger.warning(f"第 {index + 1} 行: 未找到股票代码 {stock_code} 的资金流向文件")
                self.stats['missing_files'] += 1
                self.stats['unmatched_records'] += 1
                continue
            
            # 标准化涨停日期
            target_date = self.standardize_date_format(row['涨停日期'])
            if not target_date:
                logger.warning(f"第 {index + 1} 行: 无法解析涨停日期 {row['涨停日期']}")
                self.stats['unmatched_records'] += 1
                continue
            
            # 读取并匹配资金流向数据
            try:
                fund_flow_df = pd.read_csv(matching_file, encoding='utf-8')
                
                # 检查是否有日期列
                if '日期' not in fund_flow_df.columns:
                    logger.warning(f"文件 {matching_file} 缺少'日期'列")
                    self.stats['unmatched_records'] += 1
                    continue
                
                # 标准化资金流向文件中的日期格式
                fund_flow_df['日期_标准'] = fund_flow_df['日期'].apply(self.standardize_date_format)
                
                # 查找匹配的日期
                matching_rows = fund_flow_df[fund_flow_df['日期_标准'] == target_date]
                
                if len(matching_rows) == 0:
                    logger.warning(f"第 {index + 1} 行: 在文件 {matching_file} 中未找到日期 {target_date} 的数据")
                    self.stats['date_mismatches'] += 1
                    self.stats['unmatched_records'] += 1
                    continue
                
                # 取第一条匹配记录（通常应该只有一条）
                matching_row = matching_rows.iloc[0]
                
                # 合并数据（排除日期列，避免重复）
                fund_flow_columns = [col for col in fund_flow_df.columns if col not in ['日期', '日期_标准']]
                for col in fund_flow_columns:
                    # 添加前缀以区分来源
                    new_col_name = f"资金流向_{col}"
                    self.merged_df.at[index, new_col_name] = matching_row[col]
                
                self.stats['matched_records'] += 1
                logger.debug(f"第 {index + 1} 行: 成功合并股票 {stock_code} 日期 {target_date} 的数据")
                
            except Exception as e:
                logger.error(f"第 {index + 1} 行: 处理文件 {matching_file} 时出错: {str(e)}")
                self.stats['unmatched_records'] += 1
                continue
        
        logger.info("数据合并完成")
        return True
    
    def save_result(self, output_file: Optional[str] = None) -> str:
        """保存合并结果"""
        if output_file is None:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            output_file = f"merged_data_{timestamp}.csv"
        
        try:
            self.merged_df.to_csv(output_file, index=False, encoding='utf-8-sig')
            logger.info(f"合并结果已保存到: {output_file}")
            return output_file
        except Exception as e:
            logger.error(f"保存文件失败: {str(e)}")
            raise
    
    def print_statistics(self):
        """打印处理统计信息"""
        logger.info("\n" + "="*50)
        logger.info("数据合并统计报告")
        logger.info("="*50)
        logger.info(f"总记录数: {self.stats['total_records']}")
        logger.info(f"成功匹配: {self.stats['matched_records']}")
        logger.info(f"未匹配记录: {self.stats['unmatched_records']}")
        logger.info(f"缺失文件: {self.stats['missing_files']}")
        logger.info(f"日期不匹配: {self.stats['date_mismatches']}")
        
        if self.stats['total_records'] > 0:
            success_rate = (self.stats['matched_records'] / self.stats['total_records']) * 100
            logger.info(f"匹配成功率: {success_rate:.2f}%")
        
        logger.info("="*50)

def main():
    """主函数"""
    # 配置文件路径
    main_file = r"d:\Andy\coding\gupiao_huice\1stzhangting_huice_kdj_20250601_20250630.csv"
    data_source_dir = r"d:\Andy\coding\gupiao_huice\stock_fund_flow_data"
    
    # 创建数据合并器实例
    merger = DataMerger(main_file, data_source_dir)
    
    try:
        # 验证输入
        if not merger.validate_inputs():
            logger.error("输入验证失败，程序退出")
            return
        
        # 加载主文件
        if not merger.load_main_file():
            logger.error("加载主文件失败，程序退出")
            return
        
        # 执行数据合并
        if not merger.merge_data():
            logger.error("数据合并失败，程序退出")
            return
        
        # 保存结果
        output_file = merger.save_result()
        
        # 打印统计信息
        merger.print_statistics()
        
        logger.info(f"\n程序执行完成！合并结果已保存到: {output_file}")
        
    except Exception as e:
        logger.error(f"程序执行过程中发生错误: {str(e)}")
        raise

if __name__ == "__main__":
    main()
