#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import pandas as pd
import numpy as np

def main():
    print("开始测试资金流向分析...")
    
    # 测试数据加载
    try:
        df = pd.read_csv("merged_data_20250601_20250630.csv", encoding='utf-8')
        print(f"数据加载成功: {len(df)} 条记录")
        
        # 显示前几行
        print("\n前5行数据:")
        print(df.head())
        
        # 检查资金流向列
        fund_flow_columns = [
            'FundFlow_主力净流入-净占比',
            'FundFlow_超大单净流入-净占比',
            'FundFlow_大单净流入-净占比',
            'FundFlow_中单净流入-净占比',
            'FundFlow_小单净流入-净占比'
        ]
        
        print(f"\n检查资金流向列:")
        for col in fund_flow_columns:
            if col in df.columns:
                print(f"✓ {col}")
            else:
                print(f"✗ {col}")
        
        # 检查收益率列
        if '收益率' in df.columns:
            print("✓ 收益率")
            print(f"收益率统计: 最小值={df['收益率'].min():.2f}, 最大值={df['收益率'].max():.2f}, 平均值={df['收益率'].mean():.2f}")
        else:
            print("✗ 收益率")
        
        print("\n测试完成!")
        
    except Exception as e:
        print(f"错误: {e}")

if __name__ == "__main__":
    main()
