import pandas as pd
import numpy as np

# 加载数据
df = pd.read_csv("merged_data_20250601_20250630.csv", encoding='utf-8')
print(f"数据加载成功: {len(df)} 条记录")

# 资金流向列
fund_flow_columns = [
    'FundFlow_主力净流入-净占比',
    'FundFlow_超大单净流入-净占比',
    'FundFlow_大单净流入-净占比',
    'FundFlow_中单净流入-净占比',
    'FundFlow_小单净流入-净占比'
]

# 数据清洗
for col in fund_flow_columns:
    df[col] = pd.to_numeric(df[col], errors='coerce')
df['收益率'] = pd.to_numeric(df['收益率'], errors='coerce')

df = df.dropna(subset=fund_flow_columns + ['收益率'])
print(f"清洗后数据: {len(df)} 条记录")

# 基准统计
total_records = len(df)
positive_count = (df['收益率'] > 0).sum()
positive_pct = (positive_count / total_records * 100)
gt3_count = (df['收益率'] > 3).sum()
gt3_pct = (gt3_count / total_records * 100)

print(f"\n基准统计:")
print(f"总记录: {total_records}")
print(f"正收益: {positive_count} ({positive_pct:.2f}%)")
print(f">3%收益: {gt3_count} ({gt3_pct:.2f}%)")

# 分析主力净流入为正的情况
mask = df['FundFlow_主力净流入-净占比'] > 0
filtered_df = df[mask]
f_positive_count = (filtered_df['收益率'] > 0).sum()
f_positive_pct = (f_positive_count / len(filtered_df) * 100)
f_gt3_count = (filtered_df['收益率'] > 3).sum()
f_gt3_pct = (f_gt3_count / len(filtered_df) * 100)

print(f"\n主力净流入为正值:")
print(f"记录数: {len(filtered_df)}")
print(f"正收益: {f_positive_count} ({f_positive_pct:.2f}%) 改善: {f_positive_pct - positive_pct:+.2f}%")
print(f">3%收益: {f_gt3_count} ({f_gt3_pct:.2f}%) 改善: {f_gt3_pct - gt3_pct:+.2f}%")

# 分析主力净流入>5%的情况
mask = df['FundFlow_主力净流入-净占比'] > 5
filtered_df = df[mask]
if len(filtered_df) > 0:
    f_positive_count = (filtered_df['收益率'] > 0).sum()
    f_positive_pct = (f_positive_count / len(filtered_df) * 100)
    f_gt3_count = (filtered_df['收益率'] > 3).sum()
    f_gt3_pct = (f_gt3_count / len(filtered_df) * 100)
    
    print(f"\n主力净流入>5%:")
    print(f"记录数: {len(filtered_df)}")
    print(f"正收益: {f_positive_count} ({f_positive_pct:.2f}%) 改善: {f_positive_pct - positive_pct:+.2f}%")
    print(f">3%收益: {f_gt3_count} ({f_gt3_pct:.2f}%) 改善: {f_gt3_pct - gt3_pct:+.2f}%")

# 分析所有指标为正的情况
mask = (df[fund_flow_columns] > 0).all(axis=1)
filtered_df = df[mask]
if len(filtered_df) > 0:
    f_positive_count = (filtered_df['收益率'] > 0).sum()
    f_positive_pct = (f_positive_count / len(filtered_df) * 100)
    f_gt3_count = (filtered_df['收益率'] > 3).sum()
    f_gt3_pct = (f_gt3_count / len(filtered_df) * 100)
    
    print(f"\n所有指标均为正值:")
    print(f"记录数: {len(filtered_df)}")
    print(f"正收益: {f_positive_count} ({f_positive_pct:.2f}%) 改善: {f_positive_pct - positive_pct:+.2f}%")
    print(f">3%收益: {f_gt3_count} ({f_gt3_pct:.2f}%) 改善: {f_gt3_pct - gt3_pct:+.2f}%")

print("\n分析完成！")
