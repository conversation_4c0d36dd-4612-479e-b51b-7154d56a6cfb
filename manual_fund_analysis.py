#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import pandas as pd
import numpy as np
from datetime import datetime

def main():
    print("手动执行资金流向分析...")
    
    try:
        # 加载数据
        df = pd.read_csv("merged_data_20250601_20250630.csv", encoding='utf-8')
        print(f"数据加载成功: {len(df)} 条记录")
        
        # 资金流向列
        fund_flow_columns = [
            'FundFlow_主力净流入-净占比',
            'FundFlow_超大单净流入-净占比',
            'FundFlow_大单净流入-净占比',
            'FundFlow_中单净流入-净占比',
            'FundFlow_小单净流入-净占比'
        ]
        
        # 数据清洗
        for col in fund_flow_columns:
            df[col] = pd.to_numeric(df[col], errors='coerce')
        df['收益率'] = pd.to_numeric(df['收益率'], errors='coerce')
        
        df = df.dropna(subset=fund_flow_columns + ['收益率'])
        print(f"清洗后数据: {len(df)} 条记录")
        
        # 基准统计
        total_records = len(df)
        positive_count = (df['收益率'] > 0).sum()
        positive_pct = (positive_count / total_records * 100)
        gt1_count = (df['收益率'] > 1).sum()
        gt1_pct = (gt1_count / total_records * 100)
        gt3_count = (df['收益率'] > 3).sum()
        gt3_pct = (gt3_count / total_records * 100)
        
        print(f"\n基准统计:")
        print(f"总记录: {total_records}")
        print(f"正收益: {positive_count} ({positive_pct:.2f}%)")
        print(f">1%收益: {gt1_count} ({gt1_pct:.2f}%)")
        print(f">3%收益: {gt3_count} ({gt3_pct:.2f}%)")
        
        # 分析条件
        results = []
        
        # 1. 所有指标为正
        mask = (df[fund_flow_columns] > 0).all(axis=1)
        filtered_df = df[mask]
        if len(filtered_df) > 0:
            f_positive_pct = ((filtered_df['收益率'] > 0).sum() / len(filtered_df) * 100)
            f_gt3_pct = ((filtered_df['收益率'] > 3).sum() / len(filtered_df) * 100)
            results.append({
                '条件': '所有指标均为正值',
                '记录数': len(filtered_df),
                '正收益率%': f_positive_pct,
                '>3%收益率%': f_gt3_pct,
                '正收益改善%': f_positive_pct - positive_pct,
                '>3%收益改善%': f_gt3_pct - gt3_pct
            })
        
        # 2. 主力净流入为正
        mask = df['FundFlow_主力净流入-净占比'] > 0
        filtered_df = df[mask]
        if len(filtered_df) > 0:
            f_positive_pct = ((filtered_df['收益率'] > 0).sum() / len(filtered_df) * 100)
            f_gt3_pct = ((filtered_df['收益率'] > 3).sum() / len(filtered_df) * 100)
            results.append({
                '条件': '主力净流入为正值',
                '记录数': len(filtered_df),
                '正收益率%': f_positive_pct,
                '>3%收益率%': f_gt3_pct,
                '正收益改善%': f_positive_pct - positive_pct,
                '>3%收益改善%': f_gt3_pct - gt3_pct
            })
        
        # 3. 主力净流入>5%
        mask = df['FundFlow_主力净流入-净占比'] > 5
        filtered_df = df[mask]
        if len(filtered_df) > 0:
            f_positive_pct = ((filtered_df['收益率'] > 0).sum() / len(filtered_df) * 100)
            f_gt3_pct = ((filtered_df['收益率'] > 3).sum() / len(filtered_df) * 100)
            results.append({
                '条件': '主力净流入>5%',
                '记录数': len(filtered_df),
                '正收益率%': f_positive_pct,
                '>3%收益率%': f_gt3_pct,
                '正收益改善%': f_positive_pct - positive_pct,
                '>3%收益改善%': f_gt3_pct - gt3_pct
            })
        
        # 4. 主力+超大单均为正
        mask = (df['FundFlow_主力净流入-净占比'] > 0) & (df['FundFlow_超大单净流入-净占比'] > 0)
        filtered_df = df[mask]
        if len(filtered_df) > 0:
            f_positive_pct = ((filtered_df['收益率'] > 0).sum() / len(filtered_df) * 100)
            f_gt3_pct = ((filtered_df['收益率'] > 3).sum() / len(filtered_df) * 100)
            results.append({
                '条件': '主力+超大单均为正',
                '记录数': len(filtered_df),
                '正收益率%': f_positive_pct,
                '>3%收益率%': f_gt3_pct,
                '正收益改善%': f_positive_pct - positive_pct,
                '>3%收益改善%': f_gt3_pct - gt3_pct
            })
        
        # 5. 至少3个指标为正
        mask = (df[fund_flow_columns] > 0).sum(axis=1) >= 3
        filtered_df = df[mask]
        if len(filtered_df) > 0:
            f_positive_pct = ((filtered_df['收益率'] > 0).sum() / len(filtered_df) * 100)
            f_gt3_pct = ((filtered_df['收益率'] > 3).sum() / len(filtered_df) * 100)
            results.append({
                '条件': '至少3个指标为正',
                '记录数': len(filtered_df),
                '正收益率%': f_positive_pct,
                '>3%收益率%': f_gt3_pct,
                '正收益改善%': f_positive_pct - positive_pct,
                '>3%收益改善%': f_gt3_pct - gt3_pct
            })
        
        # 6. 指标总和>10%
        mask = df[fund_flow_columns].sum(axis=1) > 10
        filtered_df = df[mask]
        if len(filtered_df) > 0:
            f_positive_pct = ((filtered_df['收益率'] > 0).sum() / len(filtered_df) * 100)
            f_gt3_pct = ((filtered_df['收益率'] > 3).sum() / len(filtered_df) * 100)
            results.append({
                '条件': '指标总和>10%',
                '记录数': len(filtered_df),
                '正收益率%': f_positive_pct,
                '>3%收益率%': f_gt3_pct,
                '正收益改善%': f_positive_pct - positive_pct,
                '>3%收益改善%': f_gt3_pct - gt3_pct
            })
        
        # 显示结果
        print(f"\n筛选条件分析结果:")
        print("-" * 80)
        for result in results:
            print(f"{result['条件']}:")
            print(f"  记录数: {result['记录数']}")
            print(f"  正收益率: {result['正收益率%']:.2f}% (改善: {result['正收益改善%']:+.2f}%)")
            print(f"  >3%收益率: {result['>3%收益率%']:.2f}% (改善: {result['>3%收益改善%']:+.2f}%)")
            print()
        
        # 保存结果
        if results:
            # 添加基准数据
            baseline = {
                '条件': '基准（无筛选）',
                '记录数': total_records,
                '正收益率%': positive_pct,
                '>3%收益率%': gt3_pct,
                '正收益改善%': 0,
                '>3%收益改善%': 0
            }
            
            all_results = [baseline] + results
            results_df = pd.DataFrame(all_results)
            
            # 按>3%收益改善率排序
            results_df = results_df.sort_values('>3%收益改善%', ascending=False)
            
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            output_file = f'资金流向分析结果_{timestamp}.csv'
            results_df.to_csv(output_file, index=False, encoding='utf-8-sig')
            print(f"结果已保存到: {output_file}")
            
            # 显示最佳条件
            print(f"\n最佳筛选条件（按>3%收益改善率排序）:")
            for i, row in results_df.head(3).iterrows():
                if row['条件'] != '基准（无筛选）':
                    print(f"{row['条件']}: >3%收益改善 {row['>3%收益改善%']:+.2f}%, 记录数 {row['记录数']}")
        
        print("\n分析完成！")
        
    except Exception as e:
        print(f"错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
