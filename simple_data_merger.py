#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化版股票数据合并程序
功能：将主文件与资金流向数据按日期和股票代码进行合并
"""

import pandas as pd
import os
import glob
from datetime import datetime

def extract_stock_code(sec_id):
    """从SecID中提取6位股票代码"""
    if pd.isna(sec_id) or not isinstance(sec_id, str):
        return ""
    return sec_id[:6]

def standardize_date_format(date_str):
    """标准化日期格式"""
    if pd.isna(date_str):
        return ""
    
    try:
        date_str = str(date_str).strip()
        
        # 处理 "2025/6/18" 格式
        if '/' in date_str:
            parts = date_str.split('/')
            if len(parts) == 3:
                year, month, day = parts
                return f"{year}-{month.zfill(2)}-{day.zfill(2)}"
        
        # 处理 "2025-06-18" 格式
        if '-' in date_str and len(date_str.split('-')) == 3:
            return date_str
        
        # 其他格式尝试用pandas解析
        parsed_date = pd.to_datetime(date_str)
        return parsed_date.strftime('%Y-%m-%d')
        
    except Exception as e:
        print(f"日期格式转换失败: {date_str}, 错误: {str(e)}")
        return ""

def find_matching_file(stock_code, data_source_dir):
    """查找匹配的资金流向文件"""
    patterns = [
        f"{stock_code}_sz_fund_flow.csv",  # 深圳交易所
        f"{stock_code}_sh_fund_flow.csv"   # 上海交易所
    ]
    
    for pattern in patterns:
        file_path = os.path.join(data_source_dir, pattern)
        if os.path.exists(file_path):
            return file_path
    
    return None

def main():
    """主函数"""
    # 配置文件路径
    main_file = r"1stzhangting_huice_kdj_20250601_20250630.csv"
    data_source_dir = r"stock_fund_flow_data"
    
    print("="*60)
    print("股票数据合并程序")
    print("="*60)
    
    # 验证输入文件和目录
    print("1. 验证输入文件和目录...")
    if not os.path.exists(main_file):
        print(f"错误：主文件不存在: {main_file}")
        return
    
    if not os.path.exists(data_source_dir):
        print(f"错误：数据源目录不存在: {data_source_dir}")
        return
    
    csv_files = glob.glob(os.path.join(data_source_dir, "*.csv"))
    if not csv_files:
        print(f"错误：数据源目录中没有找到CSV文件: {data_source_dir}")
        return
    
    print(f"   ✓ 主文件: {main_file}")
    print(f"   ✓ 数据源目录: {data_source_dir} (包含 {len(csv_files)} 个CSV文件)")
    
    # 加载主文件
    print("\n2. 加载主文件...")
    try:
        main_df = pd.read_csv(main_file, encoding='utf-8')
        print(f"   ✓ 主文件加载成功，共 {len(main_df)} 条记录")
        print(f"   ✓ 主文件列名: {list(main_df.columns)}")
        
        # 检查必要列
        required_columns = ['secID', '涨停日期']
        missing_columns = [col for col in required_columns if col not in main_df.columns]
        if missing_columns:
            print(f"错误：主文件缺少必要列: {missing_columns}")
            return
            
    except Exception as e:
        print(f"错误：加载主文件失败: {str(e)}")
        return
    
    # 初始化统计信息
    stats = {
        'total_records': len(main_df),
        'matched_records': 0,
        'unmatched_records': 0,
        'missing_files': 0,
        'date_mismatches': 0
    }
    
    # 复制主数据框用于合并
    merged_df = main_df.copy()
    
    print(f"\n3. 开始数据合并过程...")
    print(f"   总共需要处理 {stats['total_records']} 条记录")
    
    # 遍历每行数据进行合并
    for index, row in main_df.iterrows():
        # 显示进度
        if (index + 1) % 10 == 0 or index == 0:
            progress = (index + 1) / len(main_df) * 100
            print(f"   处理进度: {progress:.1f}% ({index + 1}/{len(main_df)})")
        
        # 提取股票代码
        stock_code = extract_stock_code(row['secID'])
        if not stock_code:
            print(f"   警告：第 {index + 1} 行无法提取股票代码 from {row['secID']}")
            stats['unmatched_records'] += 1
            continue
        
        # 查找匹配文件
        matching_file = find_matching_file(stock_code, data_source_dir)
        if not matching_file:
            if index < 5:  # 只显示前5个警告，避免输出过多
                print(f"   警告：第 {index + 1} 行未找到股票代码 {stock_code} 的资金流向文件")
            stats['missing_files'] += 1
            stats['unmatched_records'] += 1
            continue
        
        # 标准化涨停日期
        target_date = standardize_date_format(row['涨停日期'])
        if not target_date:
            print(f"   警告：第 {index + 1} 行无法解析涨停日期 {row['涨停日期']}")
            stats['unmatched_records'] += 1
            continue
        
        # 读取并匹配资金流向数据
        try:
            fund_flow_df = pd.read_csv(matching_file, encoding='utf-8')
            
            # 检查是否有日期列
            if '日期' not in fund_flow_df.columns:
                if index < 5:
                    print(f"   警告：文件 {matching_file} 缺少'日期'列")
                stats['unmatched_records'] += 1
                continue
            
            # 标准化资金流向文件中的日期格式
            fund_flow_df['日期_标准'] = fund_flow_df['日期'].apply(standardize_date_format)
            
            # 查找匹配的日期
            matching_rows = fund_flow_df[fund_flow_df['日期_标准'] == target_date]
            
            if len(matching_rows) == 0:
                if index < 5:
                    print(f"   警告：第 {index + 1} 行在文件中未找到日期 {target_date} 的数据")
                stats['date_mismatches'] += 1
                stats['unmatched_records'] += 1
                continue
            
            # 取第一条匹配记录
            matching_row = matching_rows.iloc[0]
            
            # 合并数据（排除日期列，避免重复）
            fund_flow_columns = [col for col in fund_flow_df.columns if col not in ['日期', '日期_标准']]
            for col in fund_flow_columns:
                # 添加前缀以区分来源
                new_col_name = f"资金流向_{col}"
                merged_df.at[index, new_col_name] = matching_row[col]
            
            stats['matched_records'] += 1
            
        except Exception as e:
            if index < 5:
                print(f"   错误：第 {index + 1} 行处理文件 {matching_file} 时出错: {str(e)}")
            stats['unmatched_records'] += 1
            continue
    
    # 保存结果
    print(f"\n4. 保存合并结果...")
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    output_file = f"merged_data_{timestamp}.csv"
    
    try:
        merged_df.to_csv(output_file, index=False, encoding='utf-8-sig')
        print(f"   ✓ 合并结果已保存到: {output_file}")
    except Exception as e:
        print(f"   错误：保存文件失败: {str(e)}")
        return
    
    # 打印统计信息
    print(f"\n5. 数据合并统计报告")
    print("="*60)
    print(f"总记录数: {stats['total_records']}")
    print(f"成功匹配: {stats['matched_records']}")
    print(f"未匹配记录: {stats['unmatched_records']}")
    print(f"缺失文件: {stats['missing_files']}")
    print(f"日期不匹配: {stats['date_mismatches']}")
    
    if stats['total_records'] > 0:
        success_rate = (stats['matched_records'] / stats['total_records']) * 100
        print(f"匹配成功率: {success_rate:.2f}%")
    
    print("="*60)
    print(f"程序执行完成！合并结果已保存到: {output_file}")

if __name__ == "__main__":
    main()
