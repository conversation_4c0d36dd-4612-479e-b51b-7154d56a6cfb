#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
新数据资金流向与收益率关系分析程序
数据文件：merged_data_20250122_20250724.csv
实现三种筛选策略：强筛选、平衡策略、宽松策略
"""

import pandas as pd
from datetime import datetime

class NewDataFundFlowAnalyzer:
    """新数据资金流向分析器"""
    
    def __init__(self, csv_file_path):
        self.csv_file_path = csv_file_path
        self.df = None
        self.fund_flow_columns = [
            'FundFlow_主力净流入-净占比',
            'FundFlow_超大单净流入-净占比',
            'FundFlow_大单净流入-净占比',
            'FundFlow_中单净流入-净占比',
            'FundFlow_小单净流入-净占比'
        ]
        self.baseline_stats = {}
        self.analysis_results = []
        
    def load_and_clean_data(self):
        """加载和清洗数据"""
        print("=" * 60)
        print("新数据资金流向与收益率关系分析")
        print("=" * 60)
        print("正在加载数据...")
        
        try:
            self.df = pd.read_csv(self.csv_file_path, encoding='utf-8')
            print(f"原始数据加载成功：{len(self.df)} 条记录")
            
            # 检查必要列
            missing_cols = [col for col in self.fund_flow_columns + ['收益率'] if col not in self.df.columns]
            if missing_cols:
                print(f"错误：缺少必要的列 {missing_cols}")
                return False
            
            # 数据类型转换
            for col in self.fund_flow_columns:
                self.df[col] = pd.to_numeric(self.df[col], errors='coerce')
            self.df['收益率'] = pd.to_numeric(self.df['收益率'], errors='coerce')
            
            # 删除空值
            original_count = len(self.df)
            self.df = self.df.dropna(subset=self.fund_flow_columns + ['收益率'])
            cleaned_count = len(self.df)
            
            print(f"数据清洗完成：{cleaned_count} 条有效记录（删除了 {original_count - cleaned_count} 条无效记录）")
            return True
            
        except Exception as e:
            print(f"数据加载失败：{str(e)}")
            return False
    
    def calculate_baseline_stats(self):
        """计算基准统计数据"""
        print("\n计算基准统计数据...")
        
        total_records = len(self.df)
        
        # 收益率>0%
        positive_mask = self.df['收益率'] > 0
        positive_count = positive_mask.sum()
        positive_mean = self.df.loc[positive_mask, '收益率'].mean() if positive_count > 0 else 0
        positive_pct = (positive_count / total_records * 100) if total_records > 0 else 0
        
        # 收益率>1%
        gt1_mask = self.df['收益率'] > 1
        gt1_count = gt1_mask.sum()
        gt1_mean = self.df.loc[gt1_mask, '收益率'].mean() if gt1_count > 0 else 0
        gt1_pct = (gt1_count / total_records * 100) if total_records > 0 else 0
        
        # 收益率>3%
        gt3_mask = self.df['收益率'] > 3
        gt3_count = gt3_mask.sum()
        gt3_mean = self.df.loc[gt3_mask, '收益率'].mean() if gt3_count > 0 else 0
        gt3_pct = (gt3_count / total_records * 100) if total_records > 0 else 0
        
        self.baseline_stats = {
            'total_records': total_records,
            'positive': {'count': positive_count, 'mean': positive_mean, 'percentage': positive_pct},
            'gt1': {'count': gt1_count, 'mean': gt1_mean, 'percentage': gt1_pct},
            'gt3': {'count': gt3_count, 'mean': gt3_mean, 'percentage': gt3_pct}
        }
        
        print(f"基准统计：")
        print(f"  总记录数: {total_records}")
        print(f"  收益率>0%: {positive_count}条 ({positive_pct:.1f}%), 平均收益率: {positive_mean:.2f}%")
        print(f"  收益率>1%: {gt1_count}条 ({gt1_pct:.1f}%), 平均收益率: {gt1_mean:.2f}%")
        print(f"  收益率>3%: {gt3_count}条 ({gt3_pct:.1f}%), 平均收益率: {gt3_mean:.2f}%")
    
    def generate_filter_conditions(self):
        """生成三种筛选策略的6个具体条件"""
        print("\n生成筛选条件...")
        
        conditions = [
            # 强筛选策略
            {
                'name': 'strong_strategy_1',
                'description': '强筛选策略1：主力净流入净占比 > 10%',
                'strategy': '强筛选策略',
                'filter_func': lambda df: df['FundFlow_主力净流入-净占比'] > 10
            },
            {
                'name': 'strong_strategy_2',
                'description': '强筛选策略2：所有5个资金流向指标均为正值',
                'strategy': '强筛选策略',
                'filter_func': lambda df: (df[self.fund_flow_columns] > 0).all(axis=1)
            },
            
            # 平衡策略
            {
                'name': 'balanced_strategy_1',
                'description': '平衡策略1：主力净流入净占比 > 5%',
                'strategy': '平衡策略',
                'filter_func': lambda df: df['FundFlow_主力净流入-净占比'] > 5
            },
            {
                'name': 'balanced_strategy_2',
                'description': '平衡策略2：至少4个资金流向指标为正值',
                'strategy': '平衡策略',
                'filter_func': lambda df: (df[self.fund_flow_columns] > 0).sum(axis=1) >= 4
            },
            
            # 宽松策略
            {
                'name': 'loose_strategy_1',
                'description': '宽松策略1：主力净流入净占比 > 0%（为正值）',
                'strategy': '宽松策略',
                'filter_func': lambda df: df['FundFlow_主力净流入-净占比'] > 0
            },
            {
                'name': 'loose_strategy_2',
                'description': '宽松策略2：至少3个资金流向指标为正值',
                'strategy': '宽松策略',
                'filter_func': lambda df: (df[self.fund_flow_columns] > 0).sum(axis=1) >= 3
            }
        ]
        
        print(f"生成了 {len(conditions)} 个筛选条件（3种策略，每种2个条件）")
        return conditions
    
    def analyze_condition(self, condition):
        """分析单个筛选条件"""
        try:
            filtered_df = self.df[condition['filter_func'](self.df)]
            total_filtered = len(filtered_df)
            
            if total_filtered == 0:
                return None
            
            # 计算各种收益率条件的统计
            results = {
                'condition_name': condition['name'],
                'description': condition['description'],
                'strategy': condition['strategy'],
                'total_records': total_filtered
            }
            
            # 收益率>0%
            positive_mask = filtered_df['收益率'] > 0
            positive_count = positive_mask.sum()
            positive_mean = filtered_df.loc[positive_mask, '收益率'].mean() if positive_count > 0 else 0
            positive_pct = (positive_count / total_filtered * 100) if total_filtered > 0 else 0
            positive_improvement = positive_pct - self.baseline_stats['positive']['percentage']
            
            results['positive'] = {
                'count': positive_count,
                'mean': positive_mean,
                'percentage': positive_pct,
                'improvement': positive_improvement
            }
            
            # 收益率>1%
            gt1_mask = filtered_df['收益率'] > 1
            gt1_count = gt1_mask.sum()
            gt1_mean = filtered_df.loc[gt1_mask, '收益率'].mean() if gt1_count > 0 else 0
            gt1_pct = (gt1_count / total_filtered * 100) if total_filtered > 0 else 0
            gt1_improvement = gt1_pct - self.baseline_stats['gt1']['percentage']
            
            results['gt1'] = {
                'count': gt1_count,
                'mean': gt1_mean,
                'percentage': gt1_pct,
                'improvement': gt1_improvement
            }
            
            # 收益率>3%
            gt3_mask = filtered_df['收益率'] > 3
            gt3_count = gt3_mask.sum()
            gt3_mean = filtered_df.loc[gt3_mask, '收益率'].mean() if gt3_count > 0 else 0
            gt3_pct = (gt3_count / total_filtered * 100) if total_filtered > 0 else 0
            gt3_improvement = gt3_pct - self.baseline_stats['gt3']['percentage']
            
            results['gt3'] = {
                'count': gt3_count,
                'mean': gt3_mean,
                'percentage': gt3_pct,
                'improvement': gt3_improvement
            }
            
            return results
            
        except Exception as e:
            print(f"分析条件 {condition['name']} 时出错: {str(e)}")
            return None
    
    def run_analysis(self):
        """运行完整分析"""
        # 加载数据
        if not self.load_and_clean_data():
            return False
        
        # 计算基准统计
        self.calculate_baseline_stats()
        
        # 生成筛选条件
        conditions = self.generate_filter_conditions()
        
        # 分析每个条件
        print("\n开始分析各个筛选条件...")
        for condition in conditions:
            print(f"分析: {condition['description']}")
            result = self.analyze_condition(condition)
            if result:
                self.analysis_results.append(result)
        
        print(f"\n分析完成，共分析了 {len(self.analysis_results)} 个筛选条件")
        return True
    
    def display_results(self):
        """显示分析结果"""
        print("\n" + "=" * 80)
        print("筛选条件分析结果")
        print("=" * 80)
        
        # 按策略分组显示
        strategies = ['强筛选策略', '平衡策略', '宽松策略']
        
        for strategy in strategies:
            print(f"\n【{strategy}】")
            print("-" * 60)
            
            strategy_results = [r for r in self.analysis_results if r['strategy'] == strategy]
            
            for result in strategy_results:
                print(f"\n{result['description']}")
                print(f"  筛选后记录数: {result['total_records']} 条")
                print(f"  收益率>0%: {result['positive']['count']}条 ({result['positive']['percentage']:.1f}%) 改善: {result['positive']['improvement']:+.1f}%")
                print(f"  收益率>1%: {result['gt1']['count']}条 ({result['gt1']['percentage']:.1f}%) 改善: {result['gt1']['improvement']:+.1f}%")
                print(f"  收益率>3%: {result['gt3']['count']}条 ({result['gt3']['percentage']:.1f}%) 改善: {result['gt3']['improvement']:+.1f}%")
    
    def export_results(self):
        """导出分析结果到CSV"""
        if not self.analysis_results:
            print("没有分析结果可导出")
            return
        
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        
        # 准备CSV数据
        csv_data = []
        
        # 添加基准数据
        baseline = self.baseline_stats
        csv_data.append({
            '筛选策略名称': '基准',
            '筛选条件描述': '基准（无筛选）',
            '筛选后记录数量': baseline['total_records'],
            '收益率>0%_数量': baseline['positive']['count'],
            '收益率>0%_平均收益率': round(baseline['positive']['mean'], 2),
            '收益率>0%_占比百分比': round(baseline['positive']['percentage'], 2),
            '收益率>1%_数量': baseline['gt1']['count'],
            '收益率>1%_平均收益率': round(baseline['gt1']['mean'], 2),
            '收益率>1%_占比百分比': round(baseline['gt1']['percentage'], 2),
            '收益率>3%_数量': baseline['gt3']['count'],
            '收益率>3%_平均收益率': round(baseline['gt3']['mean'], 2),
            '收益率>3%_占比百分比': round(baseline['gt3']['percentage'], 2),
            '相对基准改善率_正收益': 0,
            '相对基准改善率_1%以上': 0,
            '相对基准改善率_3%以上': 0
        })
        
        # 添加筛选条件结果
        for result in self.analysis_results:
            csv_data.append({
                '筛选策略名称': result['strategy'],
                '筛选条件描述': result['description'],
                '筛选后记录数量': result['total_records'],
                '收益率>0%_数量': result['positive']['count'],
                '收益率>0%_平均收益率': round(result['positive']['mean'], 2),
                '收益率>0%_占比百分比': round(result['positive']['percentage'], 2),
                '收益率>1%_数量': result['gt1']['count'],
                '收益率>1%_平均收益率': round(result['gt1']['mean'], 2),
                '收益率>1%_占比百分比': round(result['gt1']['percentage'], 2),
                '收益率>3%_数量': result['gt3']['count'],
                '收益率>3%_平均收益率': round(result['gt3']['mean'], 2),
                '收益率>3%_占比百分比': round(result['gt3']['percentage'], 2),
                '相对基准改善率_正收益': round(result['positive']['improvement'], 2),
                '相对基准改善率_1%以上': round(result['gt1']['improvement'], 2),
                '相对基准改善率_3%以上': round(result['gt3']['improvement'], 2)
            })
        
        # 创建DataFrame并保存
        results_df = pd.DataFrame(csv_data)
        
        # 按>3%收益改善率排序
        results_df = results_df.sort_values('相对基准改善率_3%以上', ascending=False)
        
        output_file = f'新数据资金流向分析结果_{timestamp}.csv'
        results_df.to_csv(output_file, index=False, encoding='utf-8-sig')
        print(f"\n分析结果已导出到: {output_file}")
        
        return output_file


def main():
    """主函数"""
    csv_file_path = r"d:\Andy\coding\gupiao_huice\merged_data_20250122_20250724.csv"
    
    analyzer = NewDataFundFlowAnalyzer(csv_file_path)
    
    if analyzer.run_analysis():
        analyzer.display_results()
        output_file = analyzer.export_results()
        
        print("\n" + "=" * 80)
        print("分析完成！")
        print("=" * 80)
        print(f"详细结果已保存到: {output_file}")
        print("请查看CSV文件获取完整的分析数据。")
    else:
        print("分析失败，请检查数据文件。")


if __name__ == "__main__":
    main()
