#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
资金流向与收益率关系深度分析程序（完整版）
作者：AI助手
日期：2025年7月26日
功能：分析资金流向指标对股票收益率的影响
"""

import pandas as pd
import numpy as np
from datetime import datetime
import os

class FundFlowAnalyzer:
    """资金流向分析器"""
    
    def __init__(self, csv_file_path):
        self.csv_file_path = csv_file_path
        self.df = None
        self.fund_flow_columns = [
            'FundFlow_主力净流入-净占比',
            'FundFlow_超大单净流入-净占比',
            'FundFlow_大单净流入-净占比',
            'FundFlow_中单净流入-净占比',
            'FundFlow_小单净流入-净占比'
        ]
        self.baseline_stats = {}
        self.analysis_results = []
        
    def load_and_clean_data(self):
        """加载和清洗数据"""
        print("正在加载数据...")
        
        try:
            self.df = pd.read_csv(self.csv_file_path, encoding='utf-8')
            print(f"原始数据加载成功：{len(self.df)} 条记录")
            
            # 检查必要列
            missing_cols = [col for col in self.fund_flow_columns + ['收益率'] if col not in self.df.columns]
            if missing_cols:
                print(f"错误：缺少必要的列 {missing_cols}")
                return False
            
            # 数据类型转换
            for col in self.fund_flow_columns:
                self.df[col] = pd.to_numeric(self.df[col], errors='coerce')
            self.df['收益率'] = pd.to_numeric(self.df['收益率'], errors='coerce')
            
            # 删除空值
            original_count = len(self.df)
            self.df = self.df.dropna(subset=self.fund_flow_columns + ['收益率'])
            cleaned_count = len(self.df)
            
            print(f"数据清洗完成：{cleaned_count} 条有效记录（删除了 {original_count - cleaned_count} 条无效记录）")
            return True
            
        except Exception as e:
            print(f"数据加载失败：{str(e)}")
            return False
    
    def calculate_baseline_stats(self):
        """计算基准统计数据"""
        print("计算基准统计数据...")
        
        total_records = len(self.df)
        
        # 收益率>0%
        positive_mask = self.df['收益率'] > 0
        positive_count = positive_mask.sum()
        positive_mean = self.df.loc[positive_mask, '收益率'].mean() if positive_count > 0 else 0
        positive_pct = (positive_count / total_records * 100) if total_records > 0 else 0
        
        # 收益率>1%
        gt1_mask = self.df['收益率'] > 1
        gt1_count = gt1_mask.sum()
        gt1_mean = self.df.loc[gt1_mask, '收益率'].mean() if gt1_count > 0 else 0
        gt1_pct = (gt1_count / total_records * 100) if total_records > 0 else 0
        
        # 收益率>3%
        gt3_mask = self.df['收益率'] > 3
        gt3_count = gt3_mask.sum()
        gt3_mean = self.df.loc[gt3_mask, '收益率'].mean() if gt3_count > 0 else 0
        gt3_pct = (gt3_count / total_records * 100) if total_records > 0 else 0
        
        self.baseline_stats = {
            'total_records': total_records,
            'positive': {'count': positive_count, 'mean': positive_mean, 'percentage': positive_pct},
            'gt1': {'count': gt1_count, 'mean': gt1_mean, 'percentage': gt1_pct},
            'gt3': {'count': gt3_count, 'mean': gt3_mean, 'percentage': gt3_pct}
        }
        
        print(f"基准统计：总记录 {total_records}，正收益 {positive_count}({positive_pct:.1f}%)，>3%收益 {gt3_count}({gt3_pct:.1f}%)")
    
    def generate_filter_conditions(self):
        """生成筛选条件"""
        print("生成筛选条件...")
        
        conditions = []
        
        # 1. 单指标正值条件
        for col in self.fund_flow_columns:
            conditions.append({
                'name': f'{col}_positive',
                'description': f'{col.split("_")[1]}为正值',
                'filter_func': lambda df, column=col: df[column] > 0
            })
        
        # 2. 单指标阈值条件
        for col in self.fund_flow_columns:
            for threshold in [5, 10, 15]:
                conditions.append({
                    'name': f'{col}_gt_{threshold}',
                    'description': f'{col.split("_")[1]} > {threshold}%',
                    'filter_func': lambda df, column=col, th=threshold: df[column] > th
                })
        
        # 3. 组合条件
        conditions.extend([
            {
                'name': 'all_positive',
                'description': '所有指标均为正值',
                'filter_func': lambda df: (df[self.fund_flow_columns] > 0).all(axis=1)
            },
            {
                'name': 'main_super_positive',
                'description': '主力+超大单均为正值',
                'filter_func': lambda df: (df['FundFlow_主力净流入-净占比'] > 0) & (df['FundFlow_超大单净流入-净占比'] > 0)
            }
        ])
        
        # 4. 多指标正值数量条件
        for min_positive in [2, 3, 4]:
            conditions.append({
                'name': f'at_least_{min_positive}_positive',
                'description': f'至少{min_positive}个指标为正值',
                'filter_func': lambda df, mp=min_positive: (df[self.fund_flow_columns] > 0).sum(axis=1) >= mp
            })
        
        # 5. 指标总和条件
        for threshold in [5, 10, 15, 20]:
            conditions.append({
                'name': f'sum_gt_{threshold}',
                'description': f'指标总和 > {threshold}%',
                'filter_func': lambda df, th=threshold: df[self.fund_flow_columns].sum(axis=1) > th
            })
        
        print(f"生成了 {len(conditions)} 个筛选条件")
        return conditions
    
    def analyze_condition(self, condition):
        """分析单个筛选条件"""
        try:
            filtered_df = self.df[condition['filter_func'](self.df)]
            total_filtered = len(filtered_df)
            
            if total_filtered == 0:
                return None
            
            # 计算各种收益率条件的统计
            results = {
                'condition_name': condition['name'],
                'description': condition['description'],
                'total_records': total_filtered
            }
            
            # 收益率>0%
            positive_mask = filtered_df['收益率'] > 0
            positive_count = positive_mask.sum()
            positive_mean = filtered_df.loc[positive_mask, '收益率'].mean() if positive_count > 0 else 0
            positive_pct = (positive_count / total_filtered * 100) if total_filtered > 0 else 0
            positive_improvement = positive_pct - self.baseline_stats['positive']['percentage']
            
            results['positive'] = {
                'count': positive_count,
                'mean': positive_mean,
                'percentage': positive_pct,
                'improvement': positive_improvement
            }
            
            # 收益率>1%
            gt1_mask = filtered_df['收益率'] > 1
            gt1_count = gt1_mask.sum()
            gt1_mean = filtered_df.loc[gt1_mask, '收益率'].mean() if gt1_count > 0 else 0
            gt1_pct = (gt1_count / total_filtered * 100) if total_filtered > 0 else 0
            gt1_improvement = gt1_pct - self.baseline_stats['gt1']['percentage']
            
            results['gt1'] = {
                'count': gt1_count,
                'mean': gt1_mean,
                'percentage': gt1_pct,
                'improvement': gt1_improvement
            }
            
            # 收益率>3%
            gt3_mask = filtered_df['收益率'] > 3
            gt3_count = gt3_mask.sum()
            gt3_mean = filtered_df.loc[gt3_mask, '收益率'].mean() if gt3_count > 0 else 0
            gt3_pct = (gt3_count / total_filtered * 100) if total_filtered > 0 else 0
            gt3_improvement = gt3_pct - self.baseline_stats['gt3']['percentage']
            
            results['gt3'] = {
                'count': gt3_count,
                'mean': gt3_mean,
                'percentage': gt3_pct,
                'improvement': gt3_improvement
            }
            
            return results
            
        except Exception as e:
            print(f"分析条件 {condition['name']} 时出错: {str(e)}")
            return None
    
    def run_analysis(self):
        """运行完整分析"""
        print("=" * 60)
        print("开始资金流向与收益率关系深度分析")
        print("=" * 60)
        
        # 加载数据
        if not self.load_and_clean_data():
            return False
        
        # 计算基准统计
        self.calculate_baseline_stats()
        
        # 生成筛选条件
        conditions = self.generate_filter_conditions()
        
        # 分析每个条件
        print("开始分析各个筛选条件...")
        for i, condition in enumerate(conditions, 1):
            if i % 10 == 0:
                print(f"分析进度: {i}/{len(conditions)}")
            
            result = self.analyze_condition(condition)
            if result and result['total_records'] >= 3:  # 至少3条记录才有意义
                self.analysis_results.append(result)
        
        print(f"分析完成，共分析了 {len(self.analysis_results)} 个有效条件")
        return True
    
    def export_results(self):
        """导出分析结果"""
        if not self.analysis_results:
            print("没有分析结果可导出")
            return
        
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        
        # 准备CSV数据
        csv_data = []
        
        # 添加基准数据
        baseline = self.baseline_stats
        csv_data.append({
            '筛选条件描述': '基准（无筛选）',
            '筛选后记录数量': baseline['total_records'],
            '收益率>0%_数量': baseline['positive']['count'],
            '收益率>0%_均值': round(baseline['positive']['mean'], 2),
            '收益率>0%_百分比': round(baseline['positive']['percentage'], 2),
            '收益率>1%_数量': baseline['gt1']['count'],
            '收益率>1%_均值': round(baseline['gt1']['mean'], 2),
            '收益率>1%_百分比': round(baseline['gt1']['percentage'], 2),
            '收益率>3%_数量': baseline['gt3']['count'],
            '收益率>3%_均值': round(baseline['gt3']['mean'], 2),
            '收益率>3%_百分比': round(baseline['gt3']['percentage'], 2),
            '相对基准改善率_正收益': 0,
            '相对基准改善率_1%以上': 0,
            '相对基准改善率_3%以上': 0
        })
        
        # 添加筛选条件结果
        for result in self.analysis_results:
            csv_data.append({
                '筛选条件描述': result['description'],
                '筛选后记录数量': result['total_records'],
                '收益率>0%_数量': result['positive']['count'],
                '收益率>0%_均值': round(result['positive']['mean'], 2),
                '收益率>0%_百分比': round(result['positive']['percentage'], 2),
                '收益率>1%_数量': result['gt1']['count'],
                '收益率>1%_均值': round(result['gt1']['mean'], 2),
                '收益率>1%_百分比': round(result['gt1']['percentage'], 2),
                '收益率>3%_数量': result['gt3']['count'],
                '收益率>3%_均值': round(result['gt3']['mean'], 2),
                '收益率>3%_百分比': round(result['gt3']['percentage'], 2),
                '相对基准改善率_正收益': round(result['positive']['improvement'], 2),
                '相对基准改善率_1%以上': round(result['gt1']['improvement'], 2),
                '相对基准改善率_3%以上': round(result['gt3']['improvement'], 2)
            })
        
        # 创建DataFrame并保存
        results_df = pd.DataFrame(csv_data)
        results_df = results_df.sort_values('相对基准改善率_3%以上', ascending=False)
        
        output_file = f'资金流向分析结果_{timestamp}.csv'
        results_df.to_csv(output_file, index=False, encoding='utf-8-sig')
        print(f"分析结果已导出到: {output_file}")
        
        # 显示最佳条件
        print("\n最佳筛选条件（按>3%收益改善率排序）:")
        print("-" * 60)
        for i, row in results_df.head(10).iterrows():
            if row['筛选条件描述'] != '基准（无筛选）':
                print(f"{row['筛选条件描述']}: 改善 {row['相对基准改善率_3%以上']:+.1f}%, 样本 {row['筛选后记录数量']}条")


def main():
    """主函数"""
    csv_file_path = "merged_data_20250601_20250630.csv"
    
    analyzer = FundFlowAnalyzer(csv_file_path)
    
    if analyzer.run_analysis():
        analyzer.export_results()
        print("\n分析完成！请查看生成的CSV文件获取详细结果。")
    else:
        print("分析失败，请检查数据文件。")


if __name__ == "__main__":
    main()
