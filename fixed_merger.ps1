# Fixed PowerShell Data Merger Script with BOM handling
# Merges main file with fund flow data by date and stock code
# Handles BOM (Byte Order Mark) issues automatically

param(
    [string]$MainFile = "1stzhangting_huice_kdj_20250122_20250724.csv",
    [string]$DataSourceDir = "stock_fund_flow_data"
)

function Clean-BOMFromText {
    param([string]$Text)
    if ([string]::IsNullOrEmpty($Text)) {
        return ""
    }
    # Remove BOM character (U+FEFF) and trim whitespace
    return $Text.TrimStart([char]0xFEFF).Trim()
}

function Extract-StockCode {
    param([string]$SecID)
    if ([string]::IsNullOrEmpty($SecID)) {
        return ""
    }
    return $SecID.Substring(0, [Math]::Min(6, $SecID.Length))
}

function Standardize-DateFormat {
    param([string]$DateStr)
    if ([string]::IsNullOrEmpty($DateStr)) {
        return ""
    }
    
    try {
        $DateStr = $DateStr.Trim()
        
        # Handle "2025/6/18" format
        if ($DateStr -match "(\d{4})/(\d{1,2})/(\d{1,2})") {
            $year = $matches[1]
            $month = $matches[2].PadLeft(2, '0')
            $day = $matches[3].PadLeft(2, '0')
            return "$year-$month-$day"
        }
        
        # Handle "2025-06-18" format
        if ($DateStr -match "^\d{4}-\d{2}-\d{2}$") {
            return $DateStr
        }
        
        return ""
    }
    catch {
        Write-Warning "Date format conversion failed: $DateStr"
        return ""
    }
}

function Find-MatchingFile {
    param(
        [string]$StockCode,
        [string]$DataSourceDir
    )
    
    $patterns = @(
        "${StockCode}_sz_fund_flow.csv",
        "${StockCode}_sh_fund_flow.csv"
    )
    
    foreach ($pattern in $patterns) {
        $filePath = Join-Path $DataSourceDir $pattern
        if (Test-Path $filePath) {
            return $filePath
        }
    }
    
    return $null
}

function Find-Column {
    param(
        [string]$TargetColumn,
        [array]$AvailableColumns
    )
    
    # Direct match
    if ($TargetColumn -in $AvailableColumns) {
        return $TargetColumn
    }
    
    # Clean BOM and match
    foreach ($col in $AvailableColumns) {
        $cleanCol = Clean-BOMFromText -Text $col
        if ($cleanCol -eq $TargetColumn) {
            return $col
        }
    }
    
    # Fuzzy match (contains target string)
    foreach ($col in $AvailableColumns) {
        $cleanCol = Clean-BOMFromText -Text $col
        if ($cleanCol -like "*$TargetColumn*" -or $TargetColumn -like "*$cleanCol*") {
            return $col
        }
    }
    
    return $null
}

# Main program starts
Write-Host "============================================================"
Write-Host "Stock Data Merger (Fixed PowerShell Version with BOM handling)"
Write-Host "============================================================"

# Validate input files and directories
Write-Host "1. Validating input files and directories..."
if (-not (Test-Path $MainFile)) {
    Write-Error "Main file does not exist: $MainFile"
    exit 1
}

if (-not (Test-Path $DataSourceDir)) {
    Write-Error "Data source directory does not exist: $DataSourceDir"
    exit 1
}

$csvFiles = Get-ChildItem -Path $DataSourceDir -Filter "*.csv"
if ($csvFiles.Count -eq 0) {
    Write-Error "No CSV files found in data source directory: $DataSourceDir"
    exit 1
}

Write-Host "   Main file: $MainFile"
Write-Host "   Data source directory: $DataSourceDir (contains $($csvFiles.Count) CSV files)"

# Load main file with BOM handling
Write-Host ""
Write-Host "2. Loading main file with BOM handling..."
try {
    # Try UTF-8 with BOM first
    $mainData = Import-Csv -Path $MainFile -Encoding UTF8
    
    # Clean BOM from column names if present
    if ($mainData.Count -gt 0) {
        $firstRow = $mainData[0]
        $originalColumns = $firstRow.PSObject.Properties.Name
        $cleanedData = @()
        
        foreach ($row in $mainData) {
            $cleanedRow = New-Object PSObject
            foreach ($prop in $row.PSObject.Properties) {
                $cleanKey = Clean-BOMFromText -Text $prop.Name
                $cleanedRow | Add-Member -MemberType NoteProperty -Name $cleanKey -Value $prop.Value
            }
            $cleanedData += $cleanedRow
        }
        $mainData = $cleanedData
    }
}
catch {
    try {
        $mainData = Import-Csv -Path $MainFile
        # Clean BOM from column names
        if ($mainData.Count -gt 0) {
            $cleanedData = @()
            foreach ($row in $mainData) {
                $cleanedRow = New-Object PSObject
                foreach ($prop in $row.PSObject.Properties) {
                    $cleanKey = Clean-BOMFromText -Text $prop.Name
                    $cleanedRow | Add-Member -MemberType NoteProperty -Name $cleanKey -Value $prop.Value
                }
                $cleanedData += $cleanedRow
            }
            $mainData = $cleanedData
        }
    }
    catch {
        Write-Error "Cannot read main file: $MainFile - $($_.Exception.Message)"
        exit 1
    }
}

Write-Host "   Main file loaded successfully, $($mainData.Count) records"

# Check required columns with intelligent matching
$availableColumns = $mainData[0].PSObject.Properties.Name
Write-Host "   Available columns: $($availableColumns -join ', ')"

# Find secID column
$secIdColumn = Find-Column -TargetColumn "secID" -AvailableColumns $availableColumns
if (-not $secIdColumn) {
    Write-Error "Cannot find secID column in main file"
    Write-Host "Available columns: $($availableColumns -join ', ')"
    exit 1
}

# Find date column - use direct approach since we can see it exists
$dateColumn = $null
foreach ($col in $availableColumns) {
    if ($col -eq "涨停日期") {
        $dateColumn = $col
        break
    }
}

# If exact match fails, try pattern matching
if (-not $dateColumn) {
    foreach ($col in $availableColumns) {
        if ($col -like "*涨停日期*" -or $col -like "*日期*") {
            $dateColumn = $col
            break
        }
    }
}

# If still not found, use the third column (index 2) as it should be the date column
if (-not $dateColumn -and $availableColumns.Count -gt 2) {
    $dateColumn = $availableColumns[2]
    Write-Host "   Using column by position: $dateColumn"
}

if (-not $dateColumn) {
    Write-Error "Cannot find date column in main file"
    Write-Host "Available columns: $($availableColumns -join ', ')"
    exit 1
}

Write-Host "   Found secID column: $secIdColumn"
Write-Host "   Found date column: $dateColumn"

# Initialize statistics
$stats = @{
    'total_records' = $mainData.Count
    'matched_records' = 0
    'unmatched_records' = 0
    'missing_files' = 0
    'date_mismatches' = 0
}

Write-Host ""
Write-Host "3. Starting data merge process..."
Write-Host "   Total records to process: $($stats.total_records)"

# Prepare merged data
$mergedData = @()

# Process each row
for ($index = 0; $index -lt $mainData.Count; $index++) {
    $row = $mainData[$index]
    
    # Show progress
    if (($index + 1) % 50 -eq 0 -or $index -eq 0) {
        $progress = ($index + 1) / $mainData.Count * 100
        Write-Host "   Progress: $([math]::Round($progress, 1))% ($($index + 1)/$($mainData.Count))"
    }
    
    # Create new row object
    $mergedRow = $row.PSObject.Copy()
    
    # Extract stock code using the found secID column
    $stockCode = Extract-StockCode -SecID $row.$secIdColumn
    if ([string]::IsNullOrEmpty($stockCode)) {
        if ($index -lt 5) {
            Write-Warning "Row $($index + 1): Cannot extract stock code from $($row.$secIdColumn)"
        }
        $stats.unmatched_records++
        $mergedData += $mergedRow
        continue
    }
    
    # Find matching file
    $matchingFile = Find-MatchingFile -StockCode $stockCode -DataSourceDir $DataSourceDir
    if (-not $matchingFile) {
        if ($index -lt 5) {
            Write-Warning "Row $($index + 1): No fund flow file found for stock code $stockCode"
        }
        $stats.missing_files++
        $stats.unmatched_records++
        $mergedData += $mergedRow
        continue
    }
    
    # Standardize target date using the found date column
    $targetDate = Standardize-DateFormat -DateStr $row.$dateColumn
    if ([string]::IsNullOrEmpty($targetDate)) {
        if ($index -lt 5) {
            Write-Warning "Row $($index + 1): Cannot parse date $($row.$dateColumn)"
        }
        $stats.unmatched_records++
        $mergedData += $mergedRow
        continue
    }
    
    # Read and match fund flow data
    try {
        $fundFlowData = Import-Csv -Path $matchingFile -Encoding UTF8
    }
    catch {
        try {
            $fundFlowData = Import-Csv -Path $matchingFile
        }
        catch {
            if ($index -lt 5) {
                Write-Warning "Row $($index + 1): Cannot read file $matchingFile"
            }
            $stats.unmatched_records++
            $mergedData += $mergedRow
            continue
        }
    }
    
    # Use the first column as date column (should be "日期")
    $fundFlowColumns = $fundFlowData[0].PSObject.Properties.Name
    $fundDateColumn = $fundFlowColumns[0]  # First column should be date
    
    # Find matching date
    $matchingRow = $null
    foreach ($fundRow in $fundFlowData) {
        $fundDate = Standardize-DateFormat -DateStr $fundRow.$fundDateColumn
        if ($fundDate -eq $targetDate) {
            $matchingRow = $fundRow
            break
        }
    }
    
    if (-not $matchingRow) {
        if ($index -lt 5) {
            Write-Warning "Row $($index + 1): No data found for date $targetDate in file"
        }
        $stats.date_mismatches++
        $stats.unmatched_records++
        $mergedData += $mergedRow
        continue
    }
    
    # Merge data (exclude date column to avoid duplication)
    $matchingRow.PSObject.Properties | Where-Object { $_.Name -ne $fundDateColumn } | ForEach-Object {
        $newColName = "FundFlow_$($_.Name)"
        $mergedRow | Add-Member -MemberType NoteProperty -Name $newColName -Value $_.Value -Force
    }
    
    $stats.matched_records++
    $mergedData += $mergedRow
}

# Save results
Write-Host ""
Write-Host "4. Saving merged results..."
$timestamp = Get-Date -Format "yyyyMMdd_HHmmss"
$outputFile = "merged_data_$timestamp.csv"

try {
    $mergedData | Export-Csv -Path $outputFile -NoTypeInformation -Encoding UTF8
    Write-Host "   Merged results saved to: $outputFile"
}
catch {
    Write-Error "Failed to save file: $($_.Exception.Message)"
    exit 1
}

# Print statistics
Write-Host ""
Write-Host "5. Data Merge Statistics Report"
Write-Host "============================================================"
Write-Host "Total records: $($stats.total_records)"
Write-Host "Successfully matched: $($stats.matched_records)"
Write-Host "Unmatched records: $($stats.unmatched_records)"
Write-Host "Missing files: $($stats.missing_files)"
Write-Host "Date mismatches: $($stats.date_mismatches)"

if ($stats.total_records -gt 0) {
    $successRate = ($stats.matched_records / $stats.total_records) * 100
    Write-Host "Match success rate: $([math]::Round($successRate, 2))%"
}

Write-Host "============================================================"
Write-Host "Program completed! Merged results saved to: $outputFile"

# Show sample of merged data
if ($mergedData.Count -gt 0) {
    Write-Host ""
    Write-Host "6. Sample of merged data (first 3 rows):"
    Write-Host "------------------------------------------------------------"
    
    $sampleColumns = @($secIdColumn, "secShortName", $dateColumn)
    $fundFlowSampleCols = $mergedData[0].PSObject.Properties.Name | Where-Object { $_ -like "FundFlow_*" } | Select-Object -First 3
    $sampleColumns += $fundFlowSampleCols
    
    for ($i = 0; $i -lt [Math]::Min(3, $mergedData.Count); $i++) {
        Write-Host ""
        Write-Host "Row $($i + 1):"
        foreach ($col in $sampleColumns) {
            if ($mergedData[$i].$col) {
                Write-Host "  $col : $($mergedData[$i].$col)"
            }
        }
    }
}
