#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化版资金流向与收益率关系分析程序
"""

import pandas as pd
import numpy as np
from datetime import datetime

def main():
    print("开始资金流向与收益率关系分析...")
    
    # 加载数据
    csv_file = r"merged_data_20250601_20250630.csv"
    print(f"加载数据文件: {csv_file}")
    
    try:
        df = pd.read_csv(csv_file, encoding='utf-8')
        print(f"数据加载成功，共 {len(df)} 条记录")
        
        # 显示列名
        print("\n数据列名:")
        for i, col in enumerate(df.columns):
            print(f"{i+1:2d}. {col}")
        
        # 资金流向列
        fund_flow_columns = [
            'FundFlow_主力净流入-净占比',
            'FundFlow_超大单净流入-净占比',
            'FundFlow_大单净流入-净占比',
            'FundFlow_中单净流入-净占比',
            'FundFlow_小单净流入-净占比'
        ]
        
        # 检查必要列是否存在
        missing_cols = [col for col in fund_flow_columns + ['收益率'] if col not in df.columns]
        if missing_cols:
            print(f"缺少必要的列: {missing_cols}")
            return
        
        # 数据清洗
        print("\n开始数据清洗...")
        original_count = len(df)
        
        # 转换数据类型并删除空值
        for col in fund_flow_columns:
            df[col] = pd.to_numeric(df[col], errors='coerce')
        df['收益率'] = pd.to_numeric(df['收益率'], errors='coerce')
        
        df = df.dropna(subset=fund_flow_columns + ['收益率'])
        cleaned_count = len(df)
        print(f"数据清洗完成: {original_count} -> {cleaned_count} 条记录")
        
        # 基准统计
        print("\n=== 基准统计（无筛选条件） ===")
        total_records = len(df)
        
        positive_mask = df['收益率'] > 0
        positive_count = positive_mask.sum()
        positive_mean = df.loc[positive_mask, '收益率'].mean() if positive_count > 0 else 0
        positive_pct = (positive_count / total_records * 100) if total_records > 0 else 0
        
        gt1_mask = df['收益率'] > 1
        gt1_count = gt1_mask.sum()
        gt1_mean = df.loc[gt1_mask, '收益率'].mean() if gt1_count > 0 else 0
        gt1_pct = (gt1_count / total_records * 100) if total_records > 0 else 0
        
        gt3_mask = df['收益率'] > 3
        gt3_count = gt3_mask.sum()
        gt3_mean = df.loc[gt3_mask, '收益率'].mean() if gt3_count > 0 else 0
        gt3_pct = (gt3_count / total_records * 100) if total_records > 0 else 0
        
        print(f"总记录数: {total_records}")
        print(f"收益率 > 0%: {positive_count} 条 ({positive_pct:.2f}%), 平均: {positive_mean:.2f}%")
        print(f"收益率 > 1%: {gt1_count} 条 ({gt1_pct:.2f}%), 平均: {gt1_mean:.2f}%")
        print(f"收益率 > 3%: {gt3_count} 条 ({gt3_pct:.2f}%), 平均: {gt3_mean:.2f}%")
        
        # 分析几个关键筛选条件
        print("\n=== 关键筛选条件分析 ===")
        
        conditions = [
            {
                'name': '所有指标均为正值',
                'filter': (df[fund_flow_columns] > 0).all(axis=1)
            },
            {
                'name': '主力净流入为正值',
                'filter': df['FundFlow_主力净流入-净占比'] > 0
            },
            {
                'name': '主力净流入 > 5%',
                'filter': df['FundFlow_主力净流入-净占比'] > 5
            },
            {
                'name': '主力+超大单均为正值',
                'filter': (df['FundFlow_主力净流入-净占比'] > 0) & (df['FundFlow_超大单净流入-净占比'] > 0)
            },
            {
                'name': '至少3个指标为正值',
                'filter': (df[fund_flow_columns] > 0).sum(axis=1) >= 3
            }
        ]
        
        results = []
        
        for condition in conditions:
            filtered_df = df[condition['filter']]
            filtered_count = len(filtered_df)
            
            if filtered_count == 0:
                print(f"\n{condition['name']}: 无符合条件的记录")
                continue
            
            # 计算统计
            f_positive_mask = filtered_df['收益率'] > 0
            f_positive_count = f_positive_mask.sum()
            f_positive_pct = (f_positive_count / filtered_count * 100) if filtered_count > 0 else 0
            f_positive_improvement = f_positive_pct - positive_pct
            
            f_gt3_mask = filtered_df['收益率'] > 3
            f_gt3_count = f_gt3_mask.sum()
            f_gt3_pct = (f_gt3_count / filtered_count * 100) if filtered_count > 0 else 0
            f_gt3_improvement = f_gt3_pct - gt3_pct
            
            print(f"\n{condition['name']}:")
            print(f"  筛选后记录: {filtered_count} 条")
            print(f"  正收益率: {f_positive_pct:.2f}% (改善: {f_positive_improvement:+.2f}%)")
            print(f"  >3%收益率: {f_gt3_pct:.2f}% (改善: {f_gt3_improvement:+.2f}%)")
            
            results.append({
                '筛选条件': condition['name'],
                '记录数': filtered_count,
                '正收益率%': f_positive_pct,
                '正收益改善%': f_positive_improvement,
                '>3%收益率%': f_gt3_pct,
                '>3%收益改善%': f_gt3_improvement
            })
        
        # 保存结果到CSV
        if results:
            results_df = pd.DataFrame(results)
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            output_file = f'fund_flow_analysis_simple_{timestamp}.csv'
            results_df.to_csv(output_file, index=False, encoding='utf-8-sig')
            print(f"\n结果已保存到: {output_file}")
        
        print("\n分析完成！")
        
    except Exception as e:
        print(f"程序执行出错: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
