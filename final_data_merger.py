#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
最终版股票数据合并程序
功能：将主文件与资金流向数据按日期和股票代码进行合并
作者：AI Assistant
创建时间：2025-07-25

使用方法：
python final_data_merger.py

输入文件：
1. 主文件：1stzhangting_huice_kdj_20250601_20250630.csv
2. 数据源目录：stock_fund_flow_data/

输出：
- 合并后的CSV文件，文件名包含时间戳
- 详细的处理统计报告
"""

import csv
import os
import glob
from datetime import datetime

def extract_stock_code(sec_id):
    """从SecID中提取6位股票代码"""
    if not sec_id or not isinstance(sec_id, str):
        return ""
    return sec_id[:6]

def standardize_date_format(date_str):
    """标准化日期格式"""
    if not date_str:
        return ""
    
    try:
        date_str = str(date_str).strip()
        
        # 处理 "2025/6/18" 格式
        if '/' in date_str:
            parts = date_str.split('/')
            if len(parts) == 3:
                year, month, day = parts
                return f"{year}-{month.zfill(2)}-{day.zfill(2)}"
        
        # 处理 "2025-06-18" 格式
        if '-' in date_str and len(date_str.split('-')) == 3:
            return date_str
        
        return ""
        
    except Exception as e:
        print(f"日期格式转换失败: {date_str}, 错误: {str(e)}")
        return ""

def clean_bom_from_text(text):
    """清理文本中的BOM字符"""
    if isinstance(text, str):
        return text.lstrip('\ufeff').strip()
    return text

def find_matching_file(stock_code, data_source_dir):
    """查找匹配的资金流向文件"""
    patterns = [
        f"{stock_code}_sz_fund_flow.csv",  # 深圳交易所
        f"{stock_code}_sh_fund_flow.csv"   # 上海交易所
    ]

    for pattern in patterns:
        file_path = os.path.join(data_source_dir, pattern)
        if os.path.exists(file_path):
            return file_path

    return None

def read_csv_file(file_path, encoding='utf-8'):
    """读取CSV文件，自动处理BOM问题"""
    try:
        # 首先尝试使用utf-8-sig编码，这会自动处理BOM
        with open(file_path, 'r', encoding='utf-8-sig', newline='') as f:
            reader = csv.DictReader(f)
            data = list(reader)
            # 清理可能残留的BOM字符
            if data:
                cleaned_data = []
                for row in data:
                    cleaned_row = {}
                    for key, value in row.items():
                        # 清理列名中的BOM字符
                        clean_key = key.lstrip('\ufeff').strip()
                        cleaned_row[clean_key] = value
                    cleaned_data.append(cleaned_row)
                return cleaned_data
            return data
    except UnicodeDecodeError:
        # 如果utf-8-sig失败，尝试其他编码
        encodings = ['utf-8', 'gbk', 'gb2312', 'cp1252']
        for enc in encodings:
            try:
                with open(file_path, 'r', encoding=enc, newline='') as f:
                    reader = csv.DictReader(f)
                    data = list(reader)
                    # 清理BOM字符
                    if data:
                        cleaned_data = []
                        for row in data:
                            cleaned_row = {}
                            for key, value in row.items():
                                clean_key = key.lstrip('\ufeff').strip()
                                cleaned_row[clean_key] = value
                            cleaned_data.append(cleaned_row)
                        return cleaned_data
                    return data
            except (UnicodeDecodeError, Exception):
                continue

        print(f"读取文件失败 {file_path}: 无法识别文件编码")
        return []
    except Exception as e:
        print(f"读取文件失败 {file_path}: {str(e)}")
        return []

def write_csv_file(file_path, data, fieldnames):
    """写入CSV文件"""
    try:
        with open(file_path, 'w', encoding='utf-8-sig', newline='') as f:
            writer = csv.DictWriter(f, fieldnames=fieldnames)
            writer.writeheader()
            writer.writerows(data)
        return True
    except Exception as e:
        print(f"写入文件失败 {file_path}: {str(e)}")
        return False

def main():
    """主函数"""
    # 配置文件路径
    main_file = r"1stzhangting_huice_kdj_20250122_20250724.csv"
    data_source_dir = r"stock_fund_flow_data"
    
    print("="*60)
    print("股票数据合并程序（最终版）")
    print("="*60)
    
    # 验证输入文件和目录
    print("1. 验证输入文件和目录...")
    if not os.path.exists(main_file):
        print(f"错误：主文件不存在: {main_file}")
        return
    
    if not os.path.exists(data_source_dir):
        print(f"错误：数据源目录不存在: {data_source_dir}")
        return
    
    csv_files = glob.glob(os.path.join(data_source_dir, "*.csv"))
    if not csv_files:
        print(f"错误：数据源目录中没有找到CSV文件: {data_source_dir}")
        return
    
    print(f"   ✓ 主文件: {main_file}")
    print(f"   ✓ 数据源目录: {data_source_dir} (包含 {len(csv_files)} 个CSV文件)")
    
    # 加载主文件
    print("\n2. 加载主文件...")
    main_data = read_csv_file(main_file)
    if not main_data:
        print(f"错误：无法读取主文件: {main_file}")
        return
    
    print(f"   ✓ 主文件加载成功，共 {len(main_data)} 条记录")
    
    # 检查必要列（智能匹配，处理BOM问题）
    if not main_data:
        print("错误：主文件为空")
        return

    first_row = main_data[0]
    available_columns = list(first_row.keys())

    # 智能查找必要列（处理BOM和编码问题）
    def find_column(target_col, available_cols):
        """智能查找列名，处理BOM和编码问题"""
        # 直接匹配
        if target_col in available_cols:
            return target_col

        # 清理BOM后匹配
        for col in available_cols:
            clean_col = clean_bom_from_text(col)
            if clean_col == target_col:
                return col

        # 模糊匹配（包含目标字符串）
        for col in available_cols:
            clean_col = clean_bom_from_text(col)
            if target_col in clean_col or clean_col in target_col:
                return col

        return None

    # 查找secID列
    secid_column = find_column('secID', available_columns)
    if not secid_column:
        print(f"错误：主文件缺少secID列")
        print(f"可用列: {available_columns}")
        print(f"清理后的列名: {[clean_bom_from_text(col) for col in available_columns]}")
        return

    # 查找日期列
    date_column = find_column('涨停日期', available_columns)
    if not date_column:
        # 尝试查找包含"日期"的列
        for col in available_columns:
            if '日期' in clean_bom_from_text(col):
                date_column = col
                break

        if not date_column:
            print(f"错误：主文件缺少日期列")
            print(f"可用列: {available_columns}")
            return

    print(f"   ✓ 找到secID列: {secid_column}")
    print(f"   ✓ 找到日期列: {date_column}")
    print(f"   ✓ 主文件列名: {available_columns}")
    
    # 初始化统计信息
    stats = {
        'total_records': len(main_data),
        'matched_records': 0,
        'unmatched_records': 0,
        'missing_files': 0,
        'date_mismatches': 0
    }
    
    print(f"\n3. 开始数据合并过程...")
    print(f"   总共需要处理 {stats['total_records']} 条记录")
    
    # 准备合并后的数据
    merged_data = []
    
    # 遍历每行数据进行合并
    for index, row in enumerate(main_data):
        # 显示进度
        if (index + 1) % 10 == 0 or index == 0:
            progress = (index + 1) / len(main_data) * 100
            print(f"   处理进度: {progress:.1f}% ({index + 1}/{len(main_data)})")
        
        # 创建新行，复制原始数据
        merged_row = row.copy()
        
        # 提取股票代码（使用找到的secID列名）
        stock_code = extract_stock_code(row.get(secid_column, ''))
        if not stock_code:
            if index < 5:
                print(f"   警告：第 {index + 1} 行无法提取股票代码 from {row.get(secid_column, '')}")
            stats['unmatched_records'] += 1
            merged_data.append(merged_row)
            continue
        
        # 查找匹配文件
        matching_file = find_matching_file(stock_code, data_source_dir)
        if not matching_file:
            if index < 5:
                print(f"   警告：第 {index + 1} 行未找到股票代码 {stock_code} 的资金流向文件")
            stats['missing_files'] += 1
            stats['unmatched_records'] += 1
            merged_data.append(merged_row)
            continue
        
        # 标准化涨停日期（使用找到的日期列名）
        target_date = standardize_date_format(row.get(date_column, ''))
        if not target_date:
            if index < 5:
                print(f"   警告：第 {index + 1} 行无法解析涨停日期 {row.get(date_column, '')}")
            stats['unmatched_records'] += 1
            merged_data.append(merged_row)
            continue
        
        # 读取并匹配资金流向数据
        fund_flow_data = read_csv_file(matching_file)
        if not fund_flow_data:
            if index < 5:
                print(f"   警告：第 {index + 1} 行无法读取文件 {matching_file}")
            stats['unmatched_records'] += 1
            merged_data.append(merged_row)
            continue
        
        # 使用第一列作为日期列（通常是"日期"）
        fund_flow_columns = list(fund_flow_data[0].keys())
        date_column = fund_flow_columns[0]
        
        # 查找匹配的日期
        matching_row = None
        for fund_row in fund_flow_data:
            fund_date = standardize_date_format(fund_row.get(date_column, ''))
            if fund_date == target_date:
                matching_row = fund_row
                break
        
        if not matching_row:
            if index < 5:
                print(f"   警告：第 {index + 1} 行在文件中未找到日期 {target_date} 的数据")
            stats['date_mismatches'] += 1
            stats['unmatched_records'] += 1
            merged_data.append(merged_row)
            continue
        
        # 合并数据（排除日期列，避免重复）
        for col, value in matching_row.items():
            if col != date_column:
                # 添加前缀以区分来源
                new_col_name = f"资金流向_{col}"
                merged_row[new_col_name] = value
        
        stats['matched_records'] += 1
        merged_data.append(merged_row)
    
    # 保存结果
    print(f"\n4. 保存合并结果...")
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    output_file = f"merged_data_{timestamp}.csv"
    
    # 获取所有字段名
    all_fieldnames = set()
    for row in merged_data:
        all_fieldnames.update(row.keys())
    all_fieldnames = sorted(list(all_fieldnames))
    
    if write_csv_file(output_file, merged_data, all_fieldnames):
        print(f"   ✓ 合并结果已保存到: {output_file}")
    else:
        print(f"   错误：保存文件失败")
        return
    
    # 打印统计信息
    print(f"\n5. 数据合并统计报告")
    print("="*60)
    print(f"总记录数: {stats['total_records']}")
    print(f"成功匹配: {stats['matched_records']}")
    print(f"未匹配记录: {stats['unmatched_records']}")
    print(f"缺失文件: {stats['missing_files']}")
    print(f"日期不匹配: {stats['date_mismatches']}")
    
    if stats['total_records'] > 0:
        success_rate = (stats['matched_records'] / stats['total_records']) * 100
        print(f"匹配成功率: {success_rate:.2f}%")
    
    print("="*60)
    print(f"程序执行完成！合并结果已保存到: {output_file}")
    
    # 显示合并后的数据示例
    print(f"\n6. 合并后数据示例（前3行）:")
    print("-" * 60)
    if merged_data:
        # 显示列名
        print("列名:")
        for i, col in enumerate(all_fieldnames):
            print(f"  {i+1:2d}. {col}")
        
        print(f"\n前3行数据:")
        for i, row in enumerate(merged_data[:3]):
            print(f"\n第 {i+1} 行:")
            # 使用实际找到的列名
            display_columns = [
                (secid_column, 'secID'),
                ('secShortName', 'secShortName'),
                (date_column, '涨停日期'),
                ('资金流向_收盘价', '资金流向_收盘价'),
                ('资金流向_涨跌幅', '资金流向_涨跌幅'),
                ('资金流向_主力净流入-净额', '资金流向_主力净流入-净额')
            ]

            for actual_col, display_name in display_columns:
                if actual_col in row:
                    print(f"  {display_name}: {row[actual_col]}")
                # 如果找不到确切列名，尝试查找相似的列名
                elif display_name.startswith('资金流向_'):
                    for col in row.keys():
                        if display_name.replace('资金流向_', '') in col:
                            print(f"  {display_name}: {row[col]}")
                            break

if __name__ == "__main__":
    main()
